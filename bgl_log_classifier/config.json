{"BIAS": "none", "DATASET_PATH": "datasets/BGL/BGL.log", "EVAL_STEPS": 500, "GRADIENT_ACCUMULATION_STEPS": 4, "LEARNING_RATE": 0.0002, "LOAD_IN_4BIT": true, "LOGGING_STEPS": 10, "LORA_ALPHA": 16, "LORA_DROPOUT": 0, "LORA_R": 16, "MAX_SEQ_LENGTH": 1024, "MODEL_NAME": "unsloth/Llama-3.2-1B-Instruct-bnb-4bit", "NUM_TRAIN_EPOCHS": 3, "OUTPUT_DIR": "./bgl_log_classifier", "PER_DEVICE_EVAL_BATCH_SIZE": 2, "PER_DEVICE_TRAIN_BATCH_SIZE": 2, "RANDOM_SEED": 42, "SAMPLE_SIZE": 1000, "SAVE_STEPS": 500, "TEST_RATIO": 0.1, "TRAIN_RATIO": 0.8, "USE_GRADIENT_CHECKPOINTING": "unsloth", "VAL_RATIO": 0.1, "WARMUP_STEPS": 5}