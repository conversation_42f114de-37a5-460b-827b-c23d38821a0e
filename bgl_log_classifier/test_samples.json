[["normal", "1120968232 2005.07.09 R27-M0-N9-C:J17-U11 2005-07-09-21.03.52.486088 R27-M0-N9-C:J17-U11 RAS KERNEL INFO generating core.27960"], ["normal", "1132703103 2005.11.22 R32-M0-N4-C:J17-U11 2005-11-22-15.45.03.327692 R32-M0-N4-C:J17-U11 RAS KERNEL INFO 1 ddr error(s) detected and corrected on rank 0, symbol 27 over 427 seconds"], ["normal", "1122165495 2005.07.23 R13-M0-NE-C:J08-U01 2005-07-23-17.38.15.222113 R13-M0-NE-C:J08-U01 RAS KERNEL INFO 5 floating point alignment exceptions"], ["normal", "1131062267 2005.11.03 R62-M0-NE-C:J12-U11 2005-11-03-15.57.47.398456 R62-M0-NE-C:J12-U11 RAS KERNEL INFO iar 00105e78 dear 02f5893c"], ["normal", "1118771897 2005.06.14 R27-M1-NB-C:J06-U01 2005-06-14-10.58.17.348215 R27-M1-NB-C:J06-U01 RAS KERNEL FATAL guaranteed data cache block touch........1"], ["normal", "1119725321 2005.06.25 R27-M1-NA-C:J08-U11 2005-06-25-11.48.41.468184 R27-M1-NA-C:J08-U11 RAS KERNEL INFO generating core.45630"], ["normal", "1119377706 2005.06.21 R21-M1-NE-C:J15-U01 2005-06-21-11.15.06.962092 R21-M1-NE-C:J15-U01 RAS KERNEL INFO generating core.1172"], ["normal", "1121256804 2005.07.13 R23-M0-N2-C:J09-U01 2005-07-13-05.13.24.575597 R23-M0-N2-C:J09-U01 RAS KERNEL INFO generating core.21470"], ["normal", "1130695761 2005.10.30 R72-M1-N0-I:J18-U11 2005-10-30-10.09.21.692728 R72-M1-N0-I:J18-U11 RAS APP FATAL ciod: Error loading /home/<USER>/HPCC_IBM/Urgent/COP/64K/hpcc-1.0.0.102905_vnm_essl_cpm: invalid or missing program image, No such file or directory"], ["normal", "1120933827 2005.07.09 R26-M0-NA-C:J08-U11 2005-07-09-11.30.27.166495 R26-M0-NA-C:J08-U11 RAS KERNEL INFO generating core.29246"], ["normal", "1133715825 2005.12.04 R62-M0-N9-C:J04-U01 2005-12-04-09.03.45.335057 R62-M0-N9-C:J04-U01 RAS KERNEL INFO CE sym 12, at 0x0353d340, mask 0x02"], ["anomaly", "1118767910 2005.06.14 R11-M1-N2-C:J09-U11 2005-06-14-09.51.50.497118 R11-M1-N2-C:J09-U11 RAS KERNEL FATAL data storage interrupt"], ["normal", "1118771860 2005.06.14 R25-M1-NC-C:J13-U11 2005-06-14-10.57.40.210240 R25-M1-NC-C:J13-U11 RAS KERNEL FATAL program interrupt: fp compare...............0"], ["normal", "1129551019 2005.10.17 R02-M0-N4-C:J04-U11 2005-10-17-05.10.19.534748 R02-M0-N4-C:J04-U11 RAS KERNEL INFO data cache search parity error detected. attempting to correct"], ["normal", "1120968221 2005.07.09 R21-M0-ND-C:J15-U01 2005-07-09-21.03.41.172892 R21-M0-ND-C:J15-U01 RAS KERNEL INFO generating core.9768"], ["normal", "1122492328 2005.07.27 R37-M1-N1-C:J08-U11 2005-07-27-12.25.28.450140 R37-M1-N1-C:J08-U11 RAS KERNEL INFO generating core.1450"], ["normal", "1118353989 2005.06.09 R20-M0-N7-C:J14-U11 2005-06-09-14.53.09.758330 R20-M0-N7-C:J14-U11 RAS KERNEL INFO generating core.3240"], ["normal", "1121313743 2005.07.13 R37-M1-N9-C:J11-U01 2005-07-13-21.02.23.127347 R37-M1-N9-C:J11-U01 RAS KERNEL INFO generating core.28449"], ["normal", "1131063572 2005.11.03 R50-M1-NA-C:J06-U01 2005-11-03-16.19.32.963322 R50-M1-NA-C:J06-U01 RAS KERNEL INFO 2354412 floating point alignment exceptions"], ["normal", "1120923620 2005.07.09 R23-M0-N1-C:J11-U11 2005-07-09-08.40.20.128152 R23-M0-N1-C:J11-U11 RAS KERNEL INFO generating core.12281"], ["anomaly", "1118536751 2005.06.11 R30-M0-N9-C:J16-U01 2005-06-11-17.39.11.402848 R30-M0-N9-C:J16-U01 RAS KERNEL FATAL data TLB error interrupt"], ["normal", "1125084576 2005.08.26 R66-M1-NA-C:J05-U11 2005-08-26-12.29.36.253684 R66-M1-NA-C:J05-U11 RAS KERNEL INFO iar 003a92a0 dear 01298008"], ["normal", "1124741746 2005.08.22 R46-M0-NC-I:J18-U11 2005-08-22-13.15.46.665016 R46-M0-NC-I:J18-U11 RAS KERNEL INFO ciod: generated 128 core files for program /home/<USER>/BGL-demo/SPaSM_mpi"], ["normal", "1118769550 2005.06.14 R27-M1-NF-C:J13-U11 2005-06-14-10.19.10.940857 R27-M1-NF-C:J13-U11 RAS KERNEL FATAL exception syndrome register: 0x00800000"], ["normal", "1124797900 2005.08.23 R05-M0-N8-I:J18-U01 2005-08-23-04.51.40.387167 R05-M0-N8-I:J18-U01 RAS APP FATAL ciod: LO<PERSON><PERSON> chdir(/home/<USER>/BGL-demo) failed: No such file or directory"], ["normal", "1118772132 2005.06.14 R24-M1-N8-C:J11-U11 2005-06-14-11.02.12.970623 R24-M1-N8-C:J11-U11 RAS KERNEL FATAL machine state register: 0x00002000"], ["normal", "1131473363 2005.11.08 R62-M0-N5-C:J12-U01 2005-11-08-10.09.23.933249 R62-M0-N5-C:J12-U01 RAS KERNEL INFO iar 00106200 dear 0247016c"], ["normal", "1127159535 2005.09.19 R06-M0-NB-C:J16-U01 2005-09-19-12.52.15.643660 R06-M0-NB-C:J16-U01 RAS KERNEL INFO CE sym 22, at 0x009c14e0, mask 0x20"], ["normal", "1125223176 2005.08.28 R53-M1-N9-C:J07-U11 2005-08-28-02.59.36.075465 R53-M1-N9-C:J07-U11 RAS KERNEL INFO iar 0014a150 dear 009971d8"], ["normal", "1131059513 2005.11.03 R67-M1-N3-C:J06-U11 2005-11-03-15.11.53.698615 R67-M1-N3-C:J06-U11 RAS KERNEL INFO iar 00106228 dear 0244c24c"], ["normal", "1128930837 2005.10.10 R24-M0-NF-C:J14-U01 2005-10-10-00.53.57.177429 R24-M0-NF-C:J14-U01 RAS KERNEL INFO 1 torus receiver x- input pipe error(s) (dcr 0x02ed) detected and corrected"], ["normal", "1131057757 2005.11.03 R65-M1-N4-C:J14-U11 2005-11-03-14.42.37.077222 R65-M1-N4-C:J14-U11 RAS KERNEL INFO 640404 floating point alignment exceptions"], ["anomaly", "1126641187 2005.09.13 R25-M0-N0-I:J18-U11 2005-09-13-12.53.07.708013 R25-M0-N0-I:J18-U11 RAS KERNEL FATAL Lustre mount FAILED : bglio337 : point /p/gb1"], ["normal", "1134024314 2005.12.07 R62-M0-N9 2005-12-07-22.45.14.321479 R62-M0-N9 NULL HARDWARE WARNING PrepareForService shutting down Node card(mLctn(R62-M0-N9), mCardSernum(203231503833343000000000594c31304b34333134303347), mLp(FF:F2:9F:16:DC:95:00:0D:60:E9:23:6A), mIp(*********), mType(4)) as part of Service Action 669"], ["normal", "1123916105 2005.08.12 R05-M1-N8-I:J18-U11 2005-08-12-23.55.05.641936 R05-M1-N8-I:J18-U11 RAS APP FATAL ciod: Error loading /bgl/apps/scaletest/performance/MINIBEN/mb_243_0810/allreduce.rts: invalid or missing program image, Exec format error"], ["normal", "1119714933 2005.06.25 R37-M1-N6-C:J13-U11 2005-06-25-08.55.33.473680 R37-M1-N6-C:J13-U11 RAS KERNEL INFO generating core.17141"], ["normal", "1125084385 2005.08.26 R64-M0-NF-C:J09-U01 2005-08-26-12.26.25.078319 R64-M0-NF-C:J09-U01 RAS KERNEL INFO 1524480 double-hummer alignment exceptions"], ["normal", "1119916096 2005.06.27 R02-M1-NB 2005-06-27-16.48.16.655216 R02-M1-NB NULL DISCOVERY SEVERE Can not get assembly information for node card"], ["normal", "1120955671 2005.07.09 R31-M1-N8-C:J03-U01 2005-07-09-17.34.31.151970 R31-M1-N8-C:J03-U01 RAS KERNEL INFO generating core.11815"], ["normal", "1131125494 2005.11.04 R62-M0-N5-C:J12-U01 2005-11-04-09.31.34.976545 R62-M0-N5-C:J12-U01 RAS KERNEL INFO iar 00106bc0 dear 0245ce5c"], ["normal", "1123110789 2005.08.03 NULL 2005-08-03-16.13.09.482844 NULL RAS MMCS ERROR idoproxydb hit ASSERT condition: ASSERT expression=0 Source file=idotransportmgr.cpp Source line=1043 Function=int IdoTransportMgr::SendPacket(IdoUdpMgr*, BglCtlPavTrace*)"], ["normal", "1120145363 2005.06.30 R21-M0-N7-C:J15-U01 2005-06-30-08.29.23.004649 R21-M0-N7-C:J15-U01 RAS KERNEL INFO generating core.17624"], ["normal", "1131123601 2005.11.04 R62-M0-N8-C:J05-U11 2005-11-04-09.00.01.155067 R62-M0-N8-C:J05-U11 RAS KERNEL INFO iar 00106588 dear 0245a17c"], ["normal", "1131060542 2005.11.03 R67-M0-NC-C:J16-U01 2005-11-03-15.29.02.071463 R67-M0-NC-C:J16-U01 RAS KERNEL INFO iar 00106534 dear 02494c3c"], ["normal", "1120469535 2005.07.04 R31-M0-NE-C:J09-U01 2005-07-04-02.32.15.877376 R31-M0-NE-C:J09-U01 RAS KERNEL INFO generating core.20518"], ["normal", "1133454545 2005.12.01 R04-M0-N5-C:J05-U11 2005-12-01-08.29.05.547331 R04-M0-N5-C:J05-U11 RAS KERNEL INFO 0 microseconds spent in the rbs signal handler during 0 calls. 0 microseconds was the maximum time for a single instance of a correctable ddr."], ["normal", "1133454609 2005.12.01 R13-M0-N6-C:J08-U11 2005-12-01-08.30.09.069793 R13-M0-N6-C:J08-U11 RAS KERNEL INFO 10756 total interrupts. 0 critical input interrupts. 0 microseconds total spent on critical input interrupts, 0 microseconds max time in a critical input interrupt."], ["normal", "1131408782 2005.11.07 R63-M0-N7-C:J15-U11 2005-11-07-16.13.02.820845 R63-M0-N7-C:J15-U11 RAS KERNEL INFO iar 00105e78 dear 02f66c9c"], ["normal", "1121707061 2005.07.18 R24-M0-N7-C:J05-U01 2005-07-18-10.17.41.184471 R24-M0-N7-C:J05-U01 RAS KERNEL INFO generating core.12523"], ["normal", "1130688103 2005.10.30 R45-M0-N0-I:J18-U01 2005-10-30-08.01.43.070508 R45-M0-N0-I:J18-U01 RAS APP FATAL ciod: Error loading /home/<USER>/HPCC_IBM/Urgent/COP/64K/hpcc-1.0.0.102905_opt_essl_cpm: invalid or missing program image, Permission denied"], ["normal", "1123103469 2005.08.03 UNKNOWN_LOCATION 2005-08-03-14.11.09.172416 UNKNOWN_LOCATION NULL DISCOVERY WARNING Node card is not fully functional"], ["normal", "1133454884 2005.12.01 R35-M0-N5-C:J07-U11 2005-12-01-08.34.44.854253 R35-M0-N5-C:J07-U11 RAS KERNEL INFO 10728 total interrupts. 0 critical input interrupts. 0 microseconds total spent on critical input interrupts, 0 microseconds max time in a critical input interrupt."], ["anomaly", "1118543004 2005.06.11 R30-M0-N9-C:J16-U01 2005-06-11-19.23.24.060605 R30-M0-N9-C:J16-U01 RAS KERNEL FATAL data TLB error interrupt"], ["normal", "1130502717 2005.10.28 R76-M1-NE-C:J03-U11 2005-10-28-05.31.57.895961 R76-M1-NE-C:J03-U11 RAS KERNEL INFO 2 tree receiver 2 in re-synch state event(s) (dcr 0x019a) detected"], ["normal", "1131060599 2005.11.03 R66-M1-NB-C:J05-U11 2005-11-03-15.29.59.483092 R66-M1-NB-C:J05-U11 RAS KERNEL INFO iar 00106534 dear 02494c3c"], ["normal", "1127243209 2005.09.20 NULL 2005-09-20-12.06.49.799879 NULL RAS MMCS ERROR idoproxydb hit ASSERT condition: ASSERT expression=0 Source file=idotransportmgr.cpp Source line=1043 Function=int IdoTransportMgr::SendPacket(IdoUdpMgr*, BglCtlPavTrace*)"], ["normal", "1132236823 2005.11.17 R67-M1-N0-I:J18-U11 2005-11-17-06.13.43.096250 R67-M1-N0-I:J18-U11 RAS KERNEL INFO ciod: Received signal 15, code=0, errno=0, address=0x000001b0"], ["anomaly", "1122322447 2005.07.25 R35-M1-N5-C:J12-U01 2005-07-25-13.14.07.227508 R35-M1-N5-C:J12-U01 RAS KERNEL FATAL rts: kernel terminated for reason 1001rts: bad message header: invalid cpu, type=10144, cpu=127, index=0, total=0"], ["normal", "1117957788 2005.06.05 R24-M0-NC-C:J15-U01 2005-06-05-00.49.48.476352 R24-M0-NC-C:J15-U01 RAS KERNEL INFO generating core.3540"], ["normal", "1120266832 2005.07.01 R36-M0-NE-C:J06-U11 2005-07-01-18.13.52.209448 R36-M0-NE-C:J06-U11 RAS KERNEL INFO 1146800 double-hummer alignment exceptions"], ["normal", "1121172329 2005.07.12 R34-M1-N2-C:J03-U11 2005-07-12-05.45.29.784440 R34-M1-N2-C:J03-U11 RAS KERNEL INFO generating core.15095"], ["normal", "1125222879 2005.08.28 R41-M1-N8-C:J09-U01 2005-08-28-02.54.39.864874 R41-M1-N8-C:J09-U01 RAS KERNEL INFO 47543876 double-hummer alignment exceptions"], ["normal", "1131238742 2005.11.05 R64-M1-N1-C:J08-U01 2005-11-05-16.59.02.138625 R64-M1-N1-C:J08-U01 RAS KERNEL INFO 1 L3 directory error(s) (dcr 0x0152) detected and corrected"], ["normal", "1118078926 2005.06.06 R02-M1-N0-C:J12-U11 2005-06-06-10.28.46.902387 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected"], ["normal", "1133724323 2005.12.04 R71-M0 2005-12-04-11.25.23.412296 R71-M0 NULL HARDWARE WARNING PrepareForService is being done on this Midplane (mLctn(R71-M0), mCardSernum(203937503631353900000000594c31304b3432393530304d), iWhichCardsToPwrOff(502)) by root"], ["normal", "1127245358 2005.09.20 R46-M1-NB 2005-09-20-12.42.38.956860 R46-M1-NB NULL DISCOVERY INFO Node card VPD check: U01 node in processor card slot J10 do not match. VPD ecid 07630DA0C62FFFFF07061AD086D1, found 04C18455962FFFFF060D1BF050EA"], ["normal", "1120490289 2005.07.04 R30-M0-NE-C:J06-U11 2005-07-04-08.18.09.563958 R30-M0-NE-C:J06-U11 RAS KERNEL INFO generating core.1078"], ["normal", "1122431457 2005.07.26 R11-M0-N6-C:J02-U01 2005-07-26-19.30.57.561850 R11-M0-N6-C:J02-U01 RAS KERNEL INFO generating core.50319"], ["normal", "1120957435 2005.07.09 R04-M1-N0-C:J12-U11 2005-07-09-18.03.55.005941 R04-M1-N0-C:J12-U11 RAS KERNEL INFO generating core.3949"], ["normal", "1122431207 2005.07.26 R10-M0-N3-C:J16-U11 2005-07-26-19.26.47.934103 R10-M0-N3-C:J16-U11 RAS KERNEL INFO generating core.28840"], ["normal", "1120972580 2005.07.09 R04-M1-N2-C:J04-U11 2005-07-09-22.16.20.144825 R04-M1-N2-C:J04-U11 RAS KERNEL INFO generating core.3695"], ["normal", "1120148714 2005.06.30 R12-M1-N4-C:J12-U11 2005-06-30-09.25.14.445693 R12-M1-N4-C:J12-U11 RAS KERNEL INFO generating core.749"], ["normal", "1118770256 2005.06.14 R25-M1-NF-C:J11-U01 2005-06-14-10.30.56.507620 R25-M1-NF-C:J11-U01 RAS KERNEL FATAL program interrupt: privileged instruction...0"], ["normal", "1121598383 2005.07.17 R25-M1-NE-C:J13-U11 2005-07-17-04.06.23.244299 R25-M1-NE-C:J13-U11 RAS KERNEL INFO generating core.8573"], ["normal", "1131582493 2005.11.09 R62-M0-N5-C:J11-U11 2005-11-09-16.28.13.329166 R62-M0-N5-C:J11-U11 RAS KERNEL INFO iar 00106274 dear 02473e1c"], ["normal", "1120975794 2005.07.09 R00-M1-NE-C:J17-U01 2005-07-09-23.09.54.433624 R00-M1-NE-C:J17-U01 RAS KERNEL INFO generating core.3092"], ["normal", "1119715812 2005.06.25 R15-M1-NA-C:J02-U11 2005-06-25-09.10.12.528622 R15-M1-NA-C:J02-U11 RAS KERNEL INFO generating core.46383"], ["normal", "1120786660 2005.07.07 R21-M0-N1-C:J15-U01 2005-07-07-18.37.40.276921 R21-M0-N1-C:J15-U01 RAS KERNEL INFO generating core.56536"], ["normal", "1131478077 2005.11.08 R63-M0-N2-C:J15-U01 2005-11-08-11.27.57.508296 R63-M0-N2-C:J15-U01 RAS KERNEL INFO iar 00106200 dear 0247019c"], ["normal", "1121442133 2005.07.15 R05-M0-N4-C:J13-U01 2005-07-15-08.42.13.913781 R05-M0-N4-C:J13-U01 RAS KERNEL INFO generating core.5493"], ["normal", "1120217134 2005.07.01 R12-M0-N2-C:J07-U01 2005-07-01-04.25.34.400090 R12-M0-N2-C:J07-U01 RAS KERNEL INFO generating core.15862"], ["normal", "1127243354 2005.09.20 NULL 2005-09-20-12.09.14.670484 NULL RAS MMCS ERROR idoproxydb hit ASSERT condition: ASSERT expression=0 Source file=idotransportmgr.cpp Source line=1043 Function=int IdoTransportMgr::SendPacket(IdoUdpMgr*, BglCtlPavTrace*)"], ["normal", "1121308481 2005.07.13 R05-M1-ND-C:J11-U01 2005-07-13-19.34.41.322525 R05-M1-ND-C:J11-U01 RAS KERNEL INFO generating core.6609"], ["normal", "1125084443 2005.08.26 R72-M1-N2-C:J11-U01 2005-08-26-12.27.23.865310 R72-M1-N2-C:J11-U01 RAS KERNEL INFO iar 003a9260 dear 00e280f8"], ["normal", "1120772087 2005.07.07 R00-M0-N6-C:J11-U11 2005-07-07-14.34.47.341098 R00-M0-N6-C:J11-U11 RAS KERNEL INFO generating core.189"], ["normal", "1122577057 2005.07.28 R33-M0-N6-C:J11-U11 2005-07-28-11.57.37.967730 R33-M0-N6-C:J11-U11 RAS KERNEL INFO generating core.5109"], ["normal", "1120940046 2005.07.09 R22-M0-N2-C:J13-U01 2005-07-09-13.14.06.007546 R22-M0-N2-C:J13-U01 RAS KERNEL INFO generating core.62429"], ["normal", "1133819436 2005.12.05 R61-M1-NF-C:J15-U11 2005-12-05-13.50.36.306779 R61-M1-NF-C:J15-U11 RAS KERNEL INFO 1 ddr error(s) detected and corrected on rank 0, symbol 13 over 420 seconds"], ["normal", "1121313873 2005.07.13 R33-M1-NA-C:J10-U11 2005-07-13-21.04.33.689728 R33-M1-NA-C:J10-U11 RAS KERNEL INFO generating core.27541"], ["normal", "1118734951 2005.06.14 R21-M1-N2-C:J16-U01 2005-06-14-00.42.31.116699 R21-M1-N2-C:J16-U01 RAS KERNEL INFO generating core.1572"], ["normal", "1118872984 2005.06.15 R37-M0-N8-C:J12-U11 2005-06-15-15.03.04.968763 R37-M0-N8-C:J12-U11 RAS KERNEL INFO generating core.3725"], ["normal", "1122166143 2005.07.23 R17-M1-NB-C:J11-U01 2005-07-23-17.49.03.120521 R17-M1-NB-C:J11-U01 RAS KERNEL INFO 5 floating point alignment exceptions"], ["normal", "1126631208 2005.09.13 R74-M1-N5-C:J07-U11 2005-09-13-10.06.48.272999 R74-M1-N5-C:J07-U11 RAS KERNEL INFO CE sym 25, at 0x1001ddc0, mask 0x01"], ["normal", "1120469742 2005.07.04 R25-M0-N5-C:J11-U11 2005-07-04-02.35.42.657006 R25-M0-N5-C:J11-U11 RAS KERNEL INFO generating core.9977"], ["normal", "1133452416 2005.12.01 R47-M1-N0-C:J13-U01 2005-12-01-07.53.36.283456 R47-M1-N0-C:J13-U01 RAS KERNEL INFO 2382 total interrupts. 0 critical input interrupts. 0 microseconds total spent on critical input interrupts, 0 microseconds max time in a critical input interrupt."], ["normal", "1121495240 2005.07.15 R36-M0-N6-C:J08-U11 2005-07-15-23.27.20.427921 R36-M0-N6-C:J08-U11 RAS KERNEL INFO generating core.33462"], ["normal", "1131126027 2005.11.04 R62-M0-NB-C:J12-U11 2005-11-04-09.40.27.568272 R62-M0-NB-C:J12-U11 RAS KERNEL INFO iar 001069a0 dear 0245ccec"], ["normal", "1133448765 2005.12.01 R21-M1-NC-C:J03-U01 2005-12-01-06.52.45.751603 R21-M1-NC-C:J03-U01 RAS KERNEL INFO 0 microseconds spent in the rbs signal handler during 0 calls. 0 microseconds was the maximum time for a single instance of a correctable ddr."], ["normal", "1117975237 2005.06.05 R26-M1-NB-C:J08-U11 2005-06-05-05.40.37.973770 R26-M1-NB-C:J08-U11 RAS KERNEL INFO generating core.266"], ["normal", "1125827732 2005.09.04 R02-M1-N0-C:J05-U11 2005-09-04-02.55.32.191901 R02-M1-N0-C:J05-U11 RAS KERNEL INFO CE sym 18, at 0x0869d1c0, mask 0x80"]]