{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 3.0, "eval_steps": 500, "global_step": 300, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.1, "grad_norm": 2.380357027053833, "learning_rate": 0.00019728813559322035, "loss": 3.3749, "step": 10}, {"epoch": 0.2, "grad_norm": 1.2666465044021606, "learning_rate": 0.0001905084745762712, "loss": 1.7046, "step": 20}, {"epoch": 0.3, "grad_norm": 0.9959502220153809, "learning_rate": 0.00018372881355932204, "loss": 1.2802, "step": 30}, {"epoch": 0.4, "grad_norm": 1.0758246183395386, "learning_rate": 0.0001769491525423729, "loss": 1.1683, "step": 40}, {"epoch": 0.5, "grad_norm": 1.230332374572754, "learning_rate": 0.00017016949152542373, "loss": 1.0267, "step": 50}, {"epoch": 0.6, "grad_norm": 1.3421251773834229, "learning_rate": 0.00016338983050847458, "loss": 0.9422, "step": 60}, {"epoch": 0.7, "grad_norm": 0.5468111634254456, "learning_rate": 0.00015661016949152542, "loss": 0.9112, "step": 70}, {"epoch": 0.8, "grad_norm": 0.6884699463844299, "learning_rate": 0.00014983050847457627, "loss": 0.845, "step": 80}, {"epoch": 0.9, "grad_norm": 0.7822244763374329, "learning_rate": 0.00014305084745762714, "loss": 0.8639, "step": 90}, {"epoch": 1.0, "grad_norm": 0.6105891466140747, "learning_rate": 0.00013627118644067798, "loss": 0.8358, "step": 100}, {"epoch": 1.1, "grad_norm": 0.7628748416900635, "learning_rate": 0.00012949152542372883, "loss": 0.7559, "step": 110}, {"epoch": 1.2, "grad_norm": 0.685671329498291, "learning_rate": 0.00012271186440677967, "loss": 0.7852, "step": 120}, {"epoch": 1.3, "grad_norm": 0.818253219127655, "learning_rate": 0.0001159322033898305, "loss": 0.7662, "step": 130}, {"epoch": 1.4, "grad_norm": 0.8048324584960938, "learning_rate": 0.00010915254237288135, "loss": 0.7325, "step": 140}, {"epoch": 1.5, "grad_norm": 0.8184697031974792, "learning_rate": 0.00010237288135593222, "loss": 0.7257, "step": 150}, {"epoch": 1.6, "grad_norm": 0.8466987609863281, "learning_rate": 9.559322033898305e-05, "loss": 0.821, "step": 160}, {"epoch": 1.7, "grad_norm": 0.7726507186889648, "learning_rate": 8.88135593220339e-05, "loss": 0.7266, "step": 170}, {"epoch": 1.8, "grad_norm": 0.7139492630958557, "learning_rate": 8.203389830508474e-05, "loss": 0.7498, "step": 180}, {"epoch": 1.9, "grad_norm": 0.7099777460098267, "learning_rate": 7.52542372881356e-05, "loss": 0.7209, "step": 190}, {"epoch": 2.0, "grad_norm": 0.8254005908966064, "learning_rate": 6.847457627118645e-05, "loss": 0.6938, "step": 200}, {"epoch": 2.1, "grad_norm": 0.7817537784576416, "learning_rate": 6.169491525423729e-05, "loss": 0.6732, "step": 210}, {"epoch": 2.2, "grad_norm": 0.9158878922462463, "learning_rate": 5.4915254237288135e-05, "loss": 0.7135, "step": 220}, {"epoch": 2.3, "grad_norm": 1.1431785821914673, "learning_rate": 4.813559322033899e-05, "loss": 0.6951, "step": 230}, {"epoch": 2.4, "grad_norm": 0.9170545935630798, "learning_rate": 4.135593220338983e-05, "loss": 0.6457, "step": 240}, {"epoch": 2.5, "grad_norm": 0.7330927848815918, "learning_rate": 3.4576271186440676e-05, "loss": 0.6601, "step": 250}, {"epoch": 2.6, "grad_norm": 1.0676883459091187, "learning_rate": 2.7796610169491528e-05, "loss": 0.6625, "step": 260}, {"epoch": 2.7, "grad_norm": 0.8769910335540771, "learning_rate": 2.1016949152542373e-05, "loss": 0.6386, "step": 270}, {"epoch": 2.8, "grad_norm": 0.6858571171760559, "learning_rate": 1.4237288135593221e-05, "loss": 0.6447, "step": 280}, {"epoch": 2.9, "grad_norm": 0.7728869915008545, "learning_rate": 7.4576271186440685e-06, "loss": 0.6365, "step": 290}, {"epoch": 3.0, "grad_norm": 0.8286202549934387, "learning_rate": 6.779661016949152e-07, "loss": 0.6445, "step": 300}], "logging_steps": 10, "max_steps": 300, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1467047097065472.0, "train_batch_size": 2, "trial_name": null, "trial_params": null}