# Centralized Dataset Management System

This document describes the new centralized dataset management system for BGL and Thunderbird log classification tasks.

## Overview

The new system provides:
- **Fixed train/test splits**: 10,000 training samples and 10,000 test samples for each dataset
- **No data leakage**: Guaranteed separation between train and test sets
- **Reproducible results**: Fixed random seed ensures consistent splits
- **Flexible subset sizes**: Support for using smaller datasets during development
- **Backward compatibility**: Legacy dataset loading methods still available

## Quick Start

### 1. Prepare Datasets

First, prepare the fixed datasets:

```bash
python prepare_datasets.py
```

This will create:
- `prepared_datasets/BGL/` - BGL train/test datasets
- `prepared_datasets/Thunderbird/` - Thunderbird train/test datasets

### 2. Use in Training Scripts

Both `bgl_log_classification.py` and `thunderbird_log_classification.py` now automatically use the fixed datasets by default.

```bash
# Train BGL model with full 10k datasets
python bgl_log_classification.py

# Train Thunderbird model with full 10k datasets  
python thunderbird_log_classification.py
```

### 3. Use Smaller Datasets for Development

To use smaller datasets during development, modify the configuration in the scripts:

```python
class Config:
    USE_FIXED_DATASETS = True
    TRAIN_SIZE = 1000  # Use only 1000 training samples
    TEST_SIZE = 500    # Use only 500 test samples
    # ... rest of config
```

## Dataset Structure

### Fixed Dataset Sizes
- **Training samples**: 10,000 per dataset
- **Test samples**: 10,000 per dataset
- **Total samples**: 20,000 per dataset

### File Structure
```
prepared_datasets/
├── BGL/
│   ├── train_samples.json      # Human-readable training data
│   ├── train_samples.pkl       # Fast-loading training data
│   ├── test_samples.json       # Human-readable test data
│   ├── test_samples.pkl        # Fast-loading test data
│   └── metadata.json           # Dataset information
└── Thunderbird/
    ├── train_samples.json
    ├── train_samples.pkl
    ├── test_samples.json
    ├── test_samples.pkl
    └── metadata.json
```

### Data Format
Each sample is a tuple: `(label, content)`
- `label`: Either "normal" or "anomaly"
- `content`: Cleaned log content (prefixes removed)

## API Reference

### DatasetManager Class

```python
from dataset_manager import DatasetManager

manager = DatasetManager()

# Get BGL data
train_samples, test_samples = manager.get_bgl_data()

# Get Thunderbird data with size limits
train_samples, test_samples = manager.get_thunderbird_data(
    train_size=1000, 
    test_size=500
)

# Print dataset information
manager.print_all_dataset_info()
```

### Convenience Functions

```python
from dataset_manager import get_bgl_train_test_data, get_thunderbird_train_test_data

# Get BGL data
train_samples, test_samples = get_bgl_train_test_data(train_size=1000)

# Get Thunderbird data
train_samples, test_samples = get_thunderbird_train_test_data(test_size=500)
```

## Configuration Options

### New Configuration (Recommended)

```python
class Config:
    # Use centralized dataset management
    USE_FIXED_DATASETS = True
    
    # Dataset sizes (None = use all available)
    TRAIN_SIZE = None  # Use all 10,000 training samples
    TEST_SIZE = None   # Use all 10,000 test samples
    
    # Validation split from training data
    VAL_RATIO = 0.1    # 10% of training data for validation
```

### Legacy Configuration (Backward Compatibility)

```python
class Config:
    # Use legacy random sampling
    USE_FIXED_DATASETS = False
    
    # Legacy settings
    DATASET_PATH = "datasets/BGL/BGL.log"
    SAMPLE_SIZE = 1000
    TRAIN_RATIO = 0.8
    TEST_RATIO = 0.1
```

## Benefits

### 1. Reproducible Results
- Fixed random seed (42) ensures identical splits across runs
- Same train/test data for fair model comparisons
- Consistent evaluation metrics

### 2. No Data Leakage
- Train and test sets are completely separate
- No overlap between training and testing data
- Proper held-out test set for final evaluation

### 3. Development Flexibility
- Use smaller subsets during development
- Scale up to full datasets for final training
- Configurable train/test sizes

### 4. Performance
- Pickle files for fast loading
- Pre-processed and cleaned data
- Efficient subset sampling

## Migration Guide

### From Legacy System

1. **Prepare datasets**: Run `python prepare_datasets.py`
2. **Update configuration**: Set `USE_FIXED_DATASETS = True`
3. **Adjust sizes**: Set `TRAIN_SIZE` and `TEST_SIZE` as needed
4. **Run training**: Use existing training scripts

### Gradual Migration

You can migrate gradually by keeping `USE_FIXED_DATASETS = False` initially and switching to `True` when ready.

## Troubleshooting

### Dataset Not Found
```
ERROR: BGL dataset not found at datasets/BGL/BGL.log
```
**Solution**: Ensure the original dataset files are downloaded and placed correctly.

### Memory Issues
```
MemoryError: Unable to allocate array
```
**Solution**: Use smaller `TRAIN_SIZE` and `TEST_SIZE` values.

### Import Errors
```
ModuleNotFoundError: No module named 'dataset_manager'
```
**Solution**: Ensure `dataset_manager.py` is in the same directory as your training scripts.

## Advanced Usage

### Custom Dataset Preparation

```python
from dataset_manager import create_fixed_dataset_split, save_dataset_split

# Create custom split
train_samples, test_samples = create_fixed_dataset_split(
    "path/to/dataset.log",
    "CustomDataset",
    total_samples=5000,
    train_size=3000,
    test_size=2000
)

# Save to custom location
save_dataset_split(train_samples, test_samples, "custom_output/", "CustomDataset")
```

### Subset Sampling

```python
from dataset_manager import get_dataset_subset

# Get random subset
subset = get_dataset_subset(full_samples, max_size=100, random_seed=42)
```

## Best Practices

1. **Always prepare datasets first**: Run `prepare_datasets.py` before training
2. **Use fixed datasets for final evaluation**: Set `USE_FIXED_DATASETS = True`
3. **Start small during development**: Use smaller `TRAIN_SIZE` and `TEST_SIZE`
4. **Document your configuration**: Note the exact sizes used for reproducibility
5. **Check dataset info**: Use `manager.print_all_dataset_info()` to verify setup
