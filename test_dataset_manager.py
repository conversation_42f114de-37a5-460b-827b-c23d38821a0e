#!/usr/bin/env python3
"""
Test script for the centralized dataset management system

This script tests the dataset manager functionality and verifies that
the train/test splits are working correctly.
"""

import os
import sys
from dataset_manager import DatasetManager, DatasetConfig


def test_dataset_manager():
    """Test the dataset manager functionality"""
    print("Testing Centralized Dataset Management System")
    print("=" * 60)
    
    # Initialize manager
    manager = DatasetManager()
    
    # Check if datasets exist
    print("\n1. Checking dataset availability...")
    
    bgl_exists = os.path.exists(DatasetConfig.BGL_DATASET_PATH)
    thunderbird_exists = os.path.exists(DatasetConfig.THUNDERBIRD_DATASET_PATH)
    
    print(f"   BGL dataset: {'✓' if bgl_exists else '✗'} {DatasetConfig.BGL_DATASET_PATH}")
    print(f"   Thunderbird dataset: {'✓' if thunderbird_exists else '✗'} {DatasetConfig.THUNDERBIRD_DATASET_PATH}")
    
    if not bgl_exists and not thunderbird_exists:
        print("\n❌ No datasets found. Please download the datasets first.")
        return False
    
    # Test dataset preparation and loading
    success_count = 0
    total_tests = 0
    
    if bgl_exists:
        print("\n2. Testing BGL dataset...")
        total_tests += 1
        try:
            # Test with small subset for speed
            train_samples, test_samples = manager.get_bgl_data(train_size=100, test_size=50)
            
            print(f"   ✓ Loaded {len(train_samples)} training samples")
            print(f"   ✓ Loaded {len(test_samples)} test samples")
            
            # Check data format
            if train_samples and len(train_samples[0]) == 2:
                label, content = train_samples[0]
                print(f"   ✓ Sample format: [{label}] {content[:50]}...")
                
            # Check for overlap
            train_contents = set(content for _, content in train_samples)
            test_contents = set(content for _, content in test_samples)
            overlap = train_contents.intersection(test_contents)
            
            if len(overlap) == 0:
                print("   ✓ No data leakage detected")
                success_count += 1
            else:
                print(f"   ❌ Data leakage detected: {len(overlap)} overlapping samples")
                
        except Exception as e:
            print(f"   ❌ Error testing BGL dataset: {e}")
    
    if thunderbird_exists:
        print("\n3. Testing Thunderbird dataset...")
        total_tests += 1
        try:
            # Test with small subset for speed
            train_samples, test_samples = manager.get_thunderbird_data(train_size=100, test_size=50)
            
            print(f"   ✓ Loaded {len(train_samples)} training samples")
            print(f"   ✓ Loaded {len(test_samples)} test samples")
            
            # Check data format
            if train_samples and len(train_samples[0]) == 2:
                label, content = train_samples[0]
                print(f"   ✓ Sample format: [{label}] {content[:50]}...")
                
            # Check for overlap
            train_contents = set(content for _, content in train_samples)
            test_contents = set(content for _, content in test_samples)
            overlap = train_contents.intersection(test_contents)
            
            if len(overlap) == 0:
                print("   ✓ No data leakage detected")
                success_count += 1
            else:
                print(f"   ❌ Data leakage detected: {len(overlap)} overlapping samples")
                
        except Exception as e:
            print(f"   ❌ Error testing Thunderbird dataset: {e}")
    
    # Test dataset info
    print("\n4. Testing dataset information...")
    try:
        manager.print_all_dataset_info()
        print("   ✓ Dataset information displayed successfully")
    except Exception as e:
        print(f"   ❌ Error displaying dataset info: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if success_count == total_tests and total_tests > 0:
        print(f"✅ All {success_count}/{total_tests} tests passed!")
        print("\nThe dataset management system is working correctly.")
        print("You can now use the centralized datasets in your training scripts.")
        return True
    else:
        print(f"❌ Only {success_count}/{total_tests} tests passed.")
        print("\nPlease check the error messages above and try again.")
        return False


def test_convenience_functions():
    """Test the convenience functions"""
    print("\n5. Testing convenience functions...")
    
    try:
        from dataset_manager import get_bgl_train_test_data, get_thunderbird_train_test_data
        
        # Test BGL convenience function
        if os.path.exists(DatasetConfig.BGL_DATASET_PATH):
            train_samples, test_samples = get_bgl_train_test_data(train_size=10, test_size=5)
            print(f"   ✓ BGL convenience function: {len(train_samples)} train, {len(test_samples)} test")
        
        # Test Thunderbird convenience function
        if os.path.exists(DatasetConfig.THUNDERBIRD_DATASET_PATH):
            train_samples, test_samples = get_thunderbird_train_test_data(train_size=10, test_size=5)
            print(f"   ✓ Thunderbird convenience function: {len(train_samples)} train, {len(test_samples)} test")
            
        print("   ✓ Convenience functions working correctly")
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing convenience functions: {e}")
        return False


def main():
    """Main test function"""
    try:
        # Test main functionality
        main_success = test_dataset_manager()
        
        # Test convenience functions
        convenience_success = test_convenience_functions()
        
        if main_success and convenience_success:
            print("\n🎉 All tests passed! The dataset management system is ready to use.")
            
            print("\nNext steps:")
            print("1. Run 'python prepare_datasets.py' to create the full 10k datasets")
            print("2. Use the updated training scripts with USE_FIXED_DATASETS = True")
            print("3. Adjust TRAIN_SIZE and TEST_SIZE as needed for your experiments")
            
            return True
        else:
            print("\n❌ Some tests failed. Please check the errors above.")
            return False
            
    except Exception as e:
        print(f"\n❌ Unexpected error during testing: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
