{"best_global_step": 1500, "best_metric": 0.5379339456558228, "best_model_checkpoint": "./bgl_log_classifier/checkpoint-1500", "epoch": 2.664888888888889, "eval_steps": 500, "global_step": 1500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.017777777777777778, "grad_norm": 2.250152826309204, "learning_rate": 0.0001995249406175772, "loss": 3.3791, "step": 10}, {"epoch": 0.035555555555555556, "grad_norm": 1.6736440658569336, "learning_rate": 0.0001983372921615202, "loss": 1.5366, "step": 20}, {"epoch": 0.05333333333333334, "grad_norm": 0.8533494472503662, "learning_rate": 0.00019714964370546318, "loss": 1.328, "step": 30}, {"epoch": 0.07111111111111111, "grad_norm": 1.1449309587478638, "learning_rate": 0.0001959619952494062, "loss": 1.1643, "step": 40}, {"epoch": 0.08888888888888889, "grad_norm": 0.9686236381530762, "learning_rate": 0.00019477434679334918, "loss": 1.1268, "step": 50}, {"epoch": 0.10666666666666667, "grad_norm": 0.8730326890945435, "learning_rate": 0.00019358669833729217, "loss": 0.9661, "step": 60}, {"epoch": 0.12444444444444444, "grad_norm": 0.7755169868469238, "learning_rate": 0.00019239904988123515, "loss": 0.8283, "step": 70}, {"epoch": 0.14222222222222222, "grad_norm": 1.0052570104599, "learning_rate": 0.00019121140142517814, "loss": 0.9522, "step": 80}, {"epoch": 0.16, "grad_norm": 0.7313763499259949, "learning_rate": 0.00019002375296912113, "loss": 0.8782, "step": 90}, {"epoch": 0.17777777777777778, "grad_norm": 1.3618526458740234, "learning_rate": 0.00018883610451306414, "loss": 0.8636, "step": 100}, {"epoch": 0.19555555555555557, "grad_norm": 0.5770422220230103, "learning_rate": 0.00018764845605700713, "loss": 0.9228, "step": 110}, {"epoch": 0.21333333333333335, "grad_norm": 1.0492353439331055, "learning_rate": 0.00018646080760095012, "loss": 0.7852, "step": 120}, {"epoch": 0.2311111111111111, "grad_norm": 0.9157597422599792, "learning_rate": 0.0001852731591448931, "loss": 0.7962, "step": 130}, {"epoch": 0.24888888888888888, "grad_norm": 0.8515543341636658, "learning_rate": 0.0001840855106888361, "loss": 0.8209, "step": 140}, {"epoch": 0.26666666666666666, "grad_norm": 0.7449517250061035, "learning_rate": 0.0001828978622327791, "loss": 0.8074, "step": 150}, {"epoch": 0.28444444444444444, "grad_norm": 0.8058556318283081, "learning_rate": 0.0001817102137767221, "loss": 0.7769, "step": 160}, {"epoch": 0.3022222222222222, "grad_norm": 0.8148768544197083, "learning_rate": 0.00018052256532066508, "loss": 0.8095, "step": 170}, {"epoch": 0.32, "grad_norm": 0.8713192343711853, "learning_rate": 0.0001793349168646081, "loss": 0.7515, "step": 180}, {"epoch": 0.3377777777777778, "grad_norm": 0.6125520467758179, "learning_rate": 0.00017814726840855108, "loss": 0.7873, "step": 190}, {"epoch": 0.35555555555555557, "grad_norm": 0.8394607305526733, "learning_rate": 0.00017695961995249407, "loss": 0.731, "step": 200}, {"epoch": 0.37333333333333335, "grad_norm": 0.7082661390304565, "learning_rate": 0.00017577197149643708, "loss": 0.7547, "step": 210}, {"epoch": 0.39111111111111113, "grad_norm": 0.7196928858757019, "learning_rate": 0.00017458432304038007, "loss": 0.7465, "step": 220}, {"epoch": 0.4088888888888889, "grad_norm": 0.6679053902626038, "learning_rate": 0.00017339667458432306, "loss": 0.6944, "step": 230}, {"epoch": 0.4266666666666667, "grad_norm": 0.6027644872665405, "learning_rate": 0.00017220902612826605, "loss": 0.7495, "step": 240}, {"epoch": 0.4444444444444444, "grad_norm": 0.6711941957473755, "learning_rate": 0.00017102137767220903, "loss": 0.747, "step": 250}, {"epoch": 0.4622222222222222, "grad_norm": 0.5501595735549927, "learning_rate": 0.00016983372921615205, "loss": 0.7204, "step": 260}, {"epoch": 0.48, "grad_norm": 0.5829436779022217, "learning_rate": 0.00016864608076009503, "loss": 0.6841, "step": 270}, {"epoch": 0.49777777777777776, "grad_norm": 0.5728204846382141, "learning_rate": 0.00016745843230403802, "loss": 0.6872, "step": 280}, {"epoch": 0.5155555555555555, "grad_norm": 0.5941566228866577, "learning_rate": 0.000166270783847981, "loss": 0.6783, "step": 290}, {"epoch": 0.5333333333333333, "grad_norm": 0.8442102074623108, "learning_rate": 0.000165083135391924, "loss": 0.7334, "step": 300}, {"epoch": 0.5511111111111111, "grad_norm": 0.7330922484397888, "learning_rate": 0.00016389548693586698, "loss": 0.6959, "step": 310}, {"epoch": 0.5688888888888889, "grad_norm": 0.6195011734962463, "learning_rate": 0.00016270783847981, "loss": 0.6603, "step": 320}, {"epoch": 0.5866666666666667, "grad_norm": 0.5233259797096252, "learning_rate": 0.00016152019002375298, "loss": 0.689, "step": 330}, {"epoch": 0.6044444444444445, "grad_norm": 0.654699444770813, "learning_rate": 0.00016033254156769597, "loss": 0.6769, "step": 340}, {"epoch": 0.6222222222222222, "grad_norm": 0.6559451222419739, "learning_rate": 0.00015914489311163896, "loss": 0.6657, "step": 350}, {"epoch": 0.64, "grad_norm": 1.0010770559310913, "learning_rate": 0.00015795724465558195, "loss": 0.6787, "step": 360}, {"epoch": 0.6577777777777778, "grad_norm": 0.6375319361686707, "learning_rate": 0.00015676959619952493, "loss": 0.672, "step": 370}, {"epoch": 0.6755555555555556, "grad_norm": 0.6828296780586243, "learning_rate": 0.00015558194774346795, "loss": 0.6568, "step": 380}, {"epoch": 0.6933333333333334, "grad_norm": 0.4177376329898834, "learning_rate": 0.00015439429928741093, "loss": 0.67, "step": 390}, {"epoch": 0.7111111111111111, "grad_norm": 0.5746023058891296, "learning_rate": 0.00015320665083135392, "loss": 0.6346, "step": 400}, {"epoch": 0.7288888888888889, "grad_norm": 0.5811883807182312, "learning_rate": 0.0001520190023752969, "loss": 0.6832, "step": 410}, {"epoch": 0.7466666666666667, "grad_norm": 0.5560966730117798, "learning_rate": 0.0001508313539192399, "loss": 0.6513, "step": 420}, {"epoch": 0.7644444444444445, "grad_norm": 0.7679100632667542, "learning_rate": 0.0001496437054631829, "loss": 0.6577, "step": 430}, {"epoch": 0.7822222222222223, "grad_norm": 0.49817830324172974, "learning_rate": 0.0001484560570071259, "loss": 0.6295, "step": 440}, {"epoch": 0.8, "grad_norm": 0.6377770304679871, "learning_rate": 0.00014726840855106888, "loss": 0.6585, "step": 450}, {"epoch": 0.8177777777777778, "grad_norm": 0.8697099685668945, "learning_rate": 0.00014608076009501187, "loss": 0.6564, "step": 460}, {"epoch": 0.8355555555555556, "grad_norm": 0.6512299180030823, "learning_rate": 0.00014489311163895486, "loss": 0.6445, "step": 470}, {"epoch": 0.8533333333333334, "grad_norm": 0.6108840107917786, "learning_rate": 0.00014370546318289785, "loss": 0.6064, "step": 480}, {"epoch": 0.8711111111111111, "grad_norm": 0.5729838013648987, "learning_rate": 0.00014251781472684086, "loss": 0.6228, "step": 490}, {"epoch": 0.8888888888888888, "grad_norm": 0.6318998336791992, "learning_rate": 0.00014133016627078385, "loss": 0.6068, "step": 500}, {"epoch": 0.8888888888888888, "eval_loss": 0.6121922135353088, "eval_runtime": 5.3699, "eval_samples_per_second": 93.112, "eval_steps_per_second": 46.556, "step": 500}, {"epoch": 0.9066666666666666, "grad_norm": 0.43409085273742676, "learning_rate": 0.00014014251781472683, "loss": 0.6569, "step": 510}, {"epoch": 0.9244444444444444, "grad_norm": 0.6554656624794006, "learning_rate": 0.00013895486935866985, "loss": 0.683, "step": 520}, {"epoch": 0.9422222222222222, "grad_norm": 0.5272390842437744, "learning_rate": 0.00013776722090261284, "loss": 0.5916, "step": 530}, {"epoch": 0.96, "grad_norm": 0.5818849802017212, "learning_rate": 0.00013657957244655582, "loss": 0.5848, "step": 540}, {"epoch": 0.9777777777777777, "grad_norm": 0.5790382623672485, "learning_rate": 0.00013539192399049884, "loss": 0.622, "step": 550}, {"epoch": 0.9955555555555555, "grad_norm": 0.8605198264122009, "learning_rate": 0.00013420427553444182, "loss": 0.6047, "step": 560}, {"epoch": 1.0124444444444445, "grad_norm": 0.5218878388404846, "learning_rate": 0.0001330166270783848, "loss": 0.6367, "step": 570}, {"epoch": 1.0302222222222222, "grad_norm": 0.7955323457717896, "learning_rate": 0.0001318289786223278, "loss": 0.58, "step": 580}, {"epoch": 1.048, "grad_norm": 0.8662599921226501, "learning_rate": 0.00013064133016627079, "loss": 0.6341, "step": 590}, {"epoch": 1.0657777777777777, "grad_norm": 0.479832261800766, "learning_rate": 0.0001294536817102138, "loss": 0.6057, "step": 600}, {"epoch": 1.0835555555555556, "grad_norm": 0.5023983120918274, "learning_rate": 0.0001282660332541568, "loss": 0.5857, "step": 610}, {"epoch": 1.1013333333333333, "grad_norm": 0.4327867925167084, "learning_rate": 0.00012707838479809978, "loss": 0.5751, "step": 620}, {"epoch": 1.1191111111111112, "grad_norm": 0.7325355410575867, "learning_rate": 0.00012589073634204276, "loss": 0.6022, "step": 630}, {"epoch": 1.1368888888888888, "grad_norm": 0.4903227984905243, "learning_rate": 0.00012470308788598575, "loss": 0.6203, "step": 640}, {"epoch": 1.1546666666666667, "grad_norm": 0.4494417905807495, "learning_rate": 0.00012351543942992874, "loss": 0.605, "step": 650}, {"epoch": 1.1724444444444444, "grad_norm": 0.7595807909965515, "learning_rate": 0.00012232779097387175, "loss": 0.5876, "step": 660}, {"epoch": 1.1902222222222223, "grad_norm": 0.5310399532318115, "learning_rate": 0.00012114014251781474, "loss": 0.5763, "step": 670}, {"epoch": 1.208, "grad_norm": 0.7179334759712219, "learning_rate": 0.00011995249406175773, "loss": 0.5964, "step": 680}, {"epoch": 1.2257777777777779, "grad_norm": 0.43397077918052673, "learning_rate": 0.00011876484560570071, "loss": 0.6129, "step": 690}, {"epoch": 1.2435555555555555, "grad_norm": 0.595554769039154, "learning_rate": 0.0001175771971496437, "loss": 0.5887, "step": 700}, {"epoch": 1.2613333333333334, "grad_norm": 0.42511674761772156, "learning_rate": 0.00011638954869358671, "loss": 0.5932, "step": 710}, {"epoch": 1.279111111111111, "grad_norm": 0.4357769787311554, "learning_rate": 0.0001152019002375297, "loss": 0.5941, "step": 720}, {"epoch": 1.2968888888888888, "grad_norm": 0.4188544452190399, "learning_rate": 0.00011401425178147269, "loss": 0.5763, "step": 730}, {"epoch": 1.3146666666666667, "grad_norm": 0.6676989197731018, "learning_rate": 0.00011282660332541568, "loss": 0.5999, "step": 740}, {"epoch": 1.3324444444444445, "grad_norm": 0.530333936214447, "learning_rate": 0.00011163895486935868, "loss": 0.5625, "step": 750}, {"epoch": 1.3502222222222222, "grad_norm": 0.6490311026573181, "learning_rate": 0.00011045130641330166, "loss": 0.5682, "step": 760}, {"epoch": 1.3679999999999999, "grad_norm": 0.5064746141433716, "learning_rate": 0.00010926365795724466, "loss": 0.5812, "step": 770}, {"epoch": 1.3857777777777778, "grad_norm": 0.5495738983154297, "learning_rate": 0.00010807600950118766, "loss": 0.5497, "step": 780}, {"epoch": 1.4035555555555557, "grad_norm": 0.5742508769035339, "learning_rate": 0.00010688836104513065, "loss": 0.5441, "step": 790}, {"epoch": 1.4213333333333333, "grad_norm": 0.5147269368171692, "learning_rate": 0.00010570071258907364, "loss": 0.5582, "step": 800}, {"epoch": 1.439111111111111, "grad_norm": 0.5988266468048096, "learning_rate": 0.00010451306413301663, "loss": 0.5953, "step": 810}, {"epoch": 1.456888888888889, "grad_norm": 0.47200852632522583, "learning_rate": 0.00010332541567695964, "loss": 0.5746, "step": 820}, {"epoch": 1.4746666666666668, "grad_norm": 0.5824215412139893, "learning_rate": 0.00010213776722090263, "loss": 0.5597, "step": 830}, {"epoch": 1.4924444444444445, "grad_norm": 0.6838793754577637, "learning_rate": 0.00010095011876484562, "loss": 0.5756, "step": 840}, {"epoch": 1.5102222222222221, "grad_norm": 0.695546567440033, "learning_rate": 9.97624703087886e-05, "loss": 0.5681, "step": 850}, {"epoch": 1.528, "grad_norm": 0.528627336025238, "learning_rate": 9.857482185273159e-05, "loss": 0.5411, "step": 860}, {"epoch": 1.545777777777778, "grad_norm": 0.5089930295944214, "learning_rate": 9.738717339667459e-05, "loss": 0.5705, "step": 870}, {"epoch": 1.5635555555555556, "grad_norm": 0.49414506554603577, "learning_rate": 9.619952494061758e-05, "loss": 0.5466, "step": 880}, {"epoch": 1.5813333333333333, "grad_norm": 0.43063896894454956, "learning_rate": 9.501187648456056e-05, "loss": 0.5641, "step": 890}, {"epoch": 1.5991111111111111, "grad_norm": 0.5886808633804321, "learning_rate": 9.382422802850357e-05, "loss": 0.583, "step": 900}, {"epoch": 1.616888888888889, "grad_norm": 0.485206663608551, "learning_rate": 9.263657957244655e-05, "loss": 0.5597, "step": 910}, {"epoch": 1.6346666666666667, "grad_norm": 0.37807539105415344, "learning_rate": 9.144893111638955e-05, "loss": 0.5583, "step": 920}, {"epoch": 1.6524444444444444, "grad_norm": 0.46195676922798157, "learning_rate": 9.026128266033254e-05, "loss": 0.5607, "step": 930}, {"epoch": 1.6702222222222223, "grad_norm": 0.537708580493927, "learning_rate": 8.907363420427554e-05, "loss": 0.5346, "step": 940}, {"epoch": 1.688, "grad_norm": 0.4963500201702118, "learning_rate": 8.788598574821854e-05, "loss": 0.5798, "step": 950}, {"epoch": 1.7057777777777776, "grad_norm": 0.7790836691856384, "learning_rate": 8.669833729216153e-05, "loss": 0.5697, "step": 960}, {"epoch": 1.7235555555555555, "grad_norm": 0.39159300923347473, "learning_rate": 8.551068883610452e-05, "loss": 0.5709, "step": 970}, {"epoch": 1.7413333333333334, "grad_norm": 0.6085025668144226, "learning_rate": 8.432304038004752e-05, "loss": 0.5609, "step": 980}, {"epoch": 1.759111111111111, "grad_norm": 0.7697580456733704, "learning_rate": 8.31353919239905e-05, "loss": 0.5973, "step": 990}, {"epoch": 1.7768888888888887, "grad_norm": 0.5112078189849854, "learning_rate": 8.194774346793349e-05, "loss": 0.5808, "step": 1000}, {"epoch": 1.7768888888888887, "eval_loss": 0.5591939687728882, "eval_runtime": 5.2574, "eval_samples_per_second": 95.104, "eval_steps_per_second": 47.552, "step": 1000}, {"epoch": 1.7946666666666666, "grad_norm": 0.43023842573165894, "learning_rate": 8.076009501187649e-05, "loss": 0.574, "step": 1010}, {"epoch": 1.8124444444444445, "grad_norm": 0.5034336447715759, "learning_rate": 7.957244655581948e-05, "loss": 0.5384, "step": 1020}, {"epoch": 1.8302222222222222, "grad_norm": 0.6216843128204346, "learning_rate": 7.838479809976247e-05, "loss": 0.5399, "step": 1030}, {"epoch": 1.8479999999999999, "grad_norm": 0.5924533009529114, "learning_rate": 7.719714964370547e-05, "loss": 0.5738, "step": 1040}, {"epoch": 1.8657777777777778, "grad_norm": 0.43152955174446106, "learning_rate": 7.600950118764845e-05, "loss": 0.5686, "step": 1050}, {"epoch": 1.8835555555555556, "grad_norm": 0.4468100070953369, "learning_rate": 7.482185273159146e-05, "loss": 0.5738, "step": 1060}, {"epoch": 1.9013333333333333, "grad_norm": 0.4129527807235718, "learning_rate": 7.363420427553444e-05, "loss": 0.5693, "step": 1070}, {"epoch": 1.919111111111111, "grad_norm": 0.6541134715080261, "learning_rate": 7.244655581947743e-05, "loss": 0.5384, "step": 1080}, {"epoch": 1.9368888888888889, "grad_norm": 0.41765451431274414, "learning_rate": 7.125890736342043e-05, "loss": 0.5483, "step": 1090}, {"epoch": 1.9546666666666668, "grad_norm": 0.5604225397109985, "learning_rate": 7.007125890736342e-05, "loss": 0.5534, "step": 1100}, {"epoch": 1.9724444444444444, "grad_norm": 0.5175177454948425, "learning_rate": 6.888361045130642e-05, "loss": 0.5267, "step": 1110}, {"epoch": 1.9902222222222221, "grad_norm": 0.5849693417549133, "learning_rate": 6.769596199524942e-05, "loss": 0.5493, "step": 1120}, {"epoch": 2.007111111111111, "grad_norm": 0.4681797921657562, "learning_rate": 6.65083135391924e-05, "loss": 0.5276, "step": 1130}, {"epoch": 2.024888888888889, "grad_norm": 0.5660867094993591, "learning_rate": 6.532066508313539e-05, "loss": 0.5324, "step": 1140}, {"epoch": 2.042666666666667, "grad_norm": 0.6120649576187134, "learning_rate": 6.41330166270784e-05, "loss": 0.5397, "step": 1150}, {"epoch": 2.0604444444444443, "grad_norm": 0.48439690470695496, "learning_rate": 6.294536817102138e-05, "loss": 0.5404, "step": 1160}, {"epoch": 2.078222222222222, "grad_norm": 0.588176429271698, "learning_rate": 6.175771971496437e-05, "loss": 0.5183, "step": 1170}, {"epoch": 2.096, "grad_norm": 0.616525411605835, "learning_rate": 6.057007125890737e-05, "loss": 0.5212, "step": 1180}, {"epoch": 2.113777777777778, "grad_norm": 0.49811816215515137, "learning_rate": 5.9382422802850356e-05, "loss": 0.5465, "step": 1190}, {"epoch": 2.1315555555555554, "grad_norm": 0.7855625152587891, "learning_rate": 5.819477434679336e-05, "loss": 0.535, "step": 1200}, {"epoch": 2.1493333333333333, "grad_norm": 0.5763819813728333, "learning_rate": 5.7007125890736344e-05, "loss": 0.5376, "step": 1210}, {"epoch": 2.167111111111111, "grad_norm": 0.47444087266921997, "learning_rate": 5.581947743467934e-05, "loss": 0.5232, "step": 1220}, {"epoch": 2.1848888888888887, "grad_norm": 0.6928284764289856, "learning_rate": 5.463182897862233e-05, "loss": 0.5291, "step": 1230}, {"epoch": 2.2026666666666666, "grad_norm": 0.5265094041824341, "learning_rate": 5.3444180522565326e-05, "loss": 0.5519, "step": 1240}, {"epoch": 2.2204444444444444, "grad_norm": 0.5240237712860107, "learning_rate": 5.225653206650831e-05, "loss": 0.5131, "step": 1250}, {"epoch": 2.2382222222222223, "grad_norm": 0.6660892963409424, "learning_rate": 5.1068883610451314e-05, "loss": 0.5135, "step": 1260}, {"epoch": 2.2560000000000002, "grad_norm": 0.5089943408966064, "learning_rate": 4.98812351543943e-05, "loss": 0.4864, "step": 1270}, {"epoch": 2.2737777777777777, "grad_norm": 0.5362017750740051, "learning_rate": 4.8693586698337295e-05, "loss": 0.5162, "step": 1280}, {"epoch": 2.2915555555555556, "grad_norm": 0.6656848788261414, "learning_rate": 4.750593824228028e-05, "loss": 0.5255, "step": 1290}, {"epoch": 2.3093333333333335, "grad_norm": 0.584794282913208, "learning_rate": 4.6318289786223276e-05, "loss": 0.5519, "step": 1300}, {"epoch": 2.327111111111111, "grad_norm": 0.5103495121002197, "learning_rate": 4.513064133016627e-05, "loss": 0.5355, "step": 1310}, {"epoch": 2.344888888888889, "grad_norm": 0.5796542167663574, "learning_rate": 4.394299287410927e-05, "loss": 0.4863, "step": 1320}, {"epoch": 2.3626666666666667, "grad_norm": 0.5910158157348633, "learning_rate": 4.275534441805226e-05, "loss": 0.5338, "step": 1330}, {"epoch": 2.3804444444444446, "grad_norm": 0.6050028204917908, "learning_rate": 4.156769596199525e-05, "loss": 0.5267, "step": 1340}, {"epoch": 2.398222222222222, "grad_norm": 0.5459794998168945, "learning_rate": 4.0380047505938246e-05, "loss": 0.5367, "step": 1350}, {"epoch": 2.416, "grad_norm": 0.7510772943496704, "learning_rate": 3.919239904988123e-05, "loss": 0.5654, "step": 1360}, {"epoch": 2.433777777777778, "grad_norm": 0.5175890922546387, "learning_rate": 3.800475059382423e-05, "loss": 0.5258, "step": 1370}, {"epoch": 2.4515555555555557, "grad_norm": 0.6697883009910583, "learning_rate": 3.681710213776722e-05, "loss": 0.5335, "step": 1380}, {"epoch": 2.469333333333333, "grad_norm": 0.5883159041404724, "learning_rate": 3.5629453681710215e-05, "loss": 0.5189, "step": 1390}, {"epoch": 2.487111111111111, "grad_norm": 0.7235488295555115, "learning_rate": 3.444180522565321e-05, "loss": 0.5337, "step": 1400}, {"epoch": 2.504888888888889, "grad_norm": 0.6540127396583557, "learning_rate": 3.32541567695962e-05, "loss": 0.5064, "step": 1410}, {"epoch": 2.522666666666667, "grad_norm": 0.5094015598297119, "learning_rate": 3.20665083135392e-05, "loss": 0.5366, "step": 1420}, {"epoch": 2.5404444444444443, "grad_norm": 0.6117748022079468, "learning_rate": 3.0878859857482184e-05, "loss": 0.5403, "step": 1430}, {"epoch": 2.558222222222222, "grad_norm": 0.6543510556221008, "learning_rate": 2.9691211401425178e-05, "loss": 0.5387, "step": 1440}, {"epoch": 2.576, "grad_norm": 0.6129150390625, "learning_rate": 2.8503562945368172e-05, "loss": 0.5354, "step": 1450}, {"epoch": 2.5937777777777775, "grad_norm": 0.49438467621803284, "learning_rate": 2.7315914489311166e-05, "loss": 0.5112, "step": 1460}, {"epoch": 2.6115555555555554, "grad_norm": 0.6483546495437622, "learning_rate": 2.6128266033254157e-05, "loss": 0.5261, "step": 1470}, {"epoch": 2.6293333333333333, "grad_norm": 0.48169928789138794, "learning_rate": 2.494061757719715e-05, "loss": 0.4931, "step": 1480}, {"epoch": 2.647111111111111, "grad_norm": 0.6143860816955566, "learning_rate": 2.375296912114014e-05, "loss": 0.4912, "step": 1490}, {"epoch": 2.664888888888889, "grad_norm": 0.5622957944869995, "learning_rate": 2.2565320665083135e-05, "loss": 0.5319, "step": 1500}, {"epoch": 2.664888888888889, "eval_loss": 0.5379339456558228, "eval_runtime": 5.2738, "eval_samples_per_second": 94.808, "eval_steps_per_second": 47.404, "step": 1500}], "logging_steps": 10, "max_steps": 1689, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 7274327556022272.0, "train_batch_size": 2, "trial_name": null, "trial_params": null}