#!/usr/bin/env python3
"""
Dataset Preparation Script for BGL and Thunderbird Log Classification

This script creates the fixed train/test datasets for both BGL and Thunderbird
log classification tasks. It generates 10,000 training samples and 10,000 test
samples for each dataset, ensuring no data leakage between splits.

Usage:
    python prepare_datasets.py [--force] [--bgl-only] [--thunderbird-only]

Options:
    --force: Force recreation of datasets even if they already exist
    --bgl-only: Only prepare BGL dataset
    --thunderbird-only: Only prepare Thunderbird dataset
"""

import argparse
import os
import sys
from dataset_manager import DatasetManager, DatasetConfig


def check_dataset_files():
    """Check if the required dataset files exist"""
    missing_files = []
    
    if not os.path.exists(DatasetConfig.BGL_DATASET_PATH):
        missing_files.append(f"BGL dataset: {DatasetConfig.BGL_DATASET_PATH}")
    
    if not os.path.exists(DatasetConfig.THUNDERBIRD_DATASET_PATH):
        missing_files.append(f"Thunderbird dataset: {DatasetConfig.THUNDERBIRD_DATASET_PATH}")
    
    if missing_files:
        print("ERROR: Missing dataset files:")
        for file in missing_files:
            print(f"  - {file}")
        print("\nPlease ensure the dataset files are downloaded and placed in the correct locations.")
        return False
    
    return True


def prepare_bgl_dataset(manager: DatasetManager, force: bool = False):
    """Prepare BGL dataset"""
    print("\n" + "="*60)
    print("PREPARING BGL DATASET")
    print("="*60)
    
    if not os.path.exists(DatasetConfig.BGL_DATASET_PATH):
        print(f"ERROR: BGL dataset not found at {DatasetConfig.BGL_DATASET_PATH}")
        return False
    
    try:
        train_samples, test_samples = manager.prepare_bgl_dataset(force_recreate=force)
        print(f"\n✅ BGL dataset prepared successfully!")
        print(f"   Training samples: {len(train_samples):,}")
        print(f"   Test samples: {len(test_samples):,}")
        return True
    except Exception as e:
        print(f"❌ Error preparing BGL dataset: {e}")
        return False


def prepare_thunderbird_dataset(manager: DatasetManager, force: bool = False):
    """Prepare Thunderbird dataset"""
    print("\n" + "="*60)
    print("PREPARING THUNDERBIRD DATASET")
    print("="*60)
    
    if not os.path.exists(DatasetConfig.THUNDERBIRD_DATASET_PATH):
        print(f"ERROR: Thunderbird dataset not found at {DatasetConfig.THUNDERBIRD_DATASET_PATH}")
        return False
    
    try:
        train_samples, test_samples = manager.prepare_thunderbird_dataset(force_recreate=force)
        print(f"\n✅ Thunderbird dataset prepared successfully!")
        print(f"   Training samples: {len(train_samples):,}")
        print(f"   Test samples: {len(test_samples):,}")
        return True
    except Exception as e:
        print(f"❌ Error preparing Thunderbird dataset: {e}")
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Prepare fixed train/test datasets for BGL and Thunderbird log classification",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python prepare_datasets.py                    # Prepare both datasets
    python prepare_datasets.py --force            # Force recreation of both datasets
    python prepare_datasets.py --bgl-only         # Only prepare BGL dataset
    python prepare_datasets.py --thunderbird-only # Only prepare Thunderbird dataset
        """
    )
    
    parser.add_argument(
        "--force", 
        action="store_true", 
        help="Force recreation of datasets even if they already exist"
    )
    parser.add_argument(
        "--bgl-only", 
        action="store_true", 
        help="Only prepare BGL dataset"
    )
    parser.add_argument(
        "--thunderbird-only", 
        action="store_true", 
        help="Only prepare Thunderbird dataset"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.bgl_only and args.thunderbird_only:
        print("ERROR: Cannot specify both --bgl-only and --thunderbird-only")
        sys.exit(1)
    
    print("Dataset Preparation Script")
    print("=" * 60)
    print(f"Configuration:")
    print(f"  Train samples per dataset: {DatasetConfig.TRAIN_SIZE:,}")
    print(f"  Test samples per dataset: {DatasetConfig.TEST_SIZE:,}")
    print(f"  Total samples per dataset: {DatasetConfig.TOTAL_SAMPLES:,}")
    print(f"  Random seed: {DatasetConfig.RANDOM_SEED}")
    print(f"  Output directory: {DatasetConfig.DATASETS_OUTPUT_DIR}")
    
    # Check if dataset files exist
    if not check_dataset_files():
        sys.exit(1)
    
    # Initialize dataset manager
    manager = DatasetManager()
    
    # Track success
    success_count = 0
    total_count = 0
    
    # Prepare datasets based on arguments
    if not args.thunderbird_only:
        total_count += 1
        if prepare_bgl_dataset(manager, args.force):
            success_count += 1
    
    if not args.bgl_only:
        total_count += 1
        if prepare_thunderbird_dataset(manager, args.force):
            success_count += 1
    
    # Print summary
    print("\n" + "="*60)
    print("PREPARATION SUMMARY")
    print("="*60)
    
    if success_count == total_count:
        print(f"✅ Successfully prepared {success_count}/{total_count} datasets!")
        
        # Show dataset information
        manager.print_all_dataset_info()
        
        print(f"\nDatasets are ready for use!")
        print(f"You can now run the classification scripts with the prepared datasets.")
        
    else:
        print(f"❌ Only {success_count}/{total_count} datasets prepared successfully.")
        print(f"Please check the error messages above and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()
