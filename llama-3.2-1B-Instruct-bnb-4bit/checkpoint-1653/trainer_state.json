{"best_global_step": 1500, "best_metric": 0.38953956961631775, "best_model_checkpoint": "./llama-3.2-1B-Instruct-bnb-4bit/checkpoint-1500", "epoch": 3.0, "eval_steps": 500, "global_step": 1653, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.018157058556513846, "grad_norm": 1.7560596466064453, "learning_rate": 0.00019951456310679614, "loss": 3.7856, "step": 10}, {"epoch": 0.03631411711302769, "grad_norm": 1.8364932537078857, "learning_rate": 0.00019830097087378642, "loss": 2.0858, "step": 20}, {"epoch": 0.05447117566954154, "grad_norm": 1.5303274393081665, "learning_rate": 0.0001970873786407767, "loss": 1.3924, "step": 30}, {"epoch": 0.07262823422605538, "grad_norm": 1.63041353225708, "learning_rate": 0.000195873786407767, "loss": 1.2197, "step": 40}, {"epoch": 0.09078529278256922, "grad_norm": 1.8466845750808716, "learning_rate": 0.00019466019417475728, "loss": 1.0676, "step": 50}, {"epoch": 0.10894235133908307, "grad_norm": 1.3585405349731445, "learning_rate": 0.00019344660194174756, "loss": 0.9172, "step": 60}, {"epoch": 0.12709940989559693, "grad_norm": 1.0299218893051147, "learning_rate": 0.00019223300970873787, "loss": 0.7956, "step": 70}, {"epoch": 0.14525646845211077, "grad_norm": 1.2935006618499756, "learning_rate": 0.00019101941747572815, "loss": 0.7831, "step": 80}, {"epoch": 0.1634135270086246, "grad_norm": 1.2748897075653076, "learning_rate": 0.00018980582524271846, "loss": 0.7841, "step": 90}, {"epoch": 0.18157058556513844, "grad_norm": 1.2339545488357544, "learning_rate": 0.00018859223300970874, "loss": 0.7005, "step": 100}, {"epoch": 0.19972764412165228, "grad_norm": 0.9671828150749207, "learning_rate": 0.00018737864077669904, "loss": 0.7353, "step": 110}, {"epoch": 0.21788470267816615, "grad_norm": 0.9775391221046448, "learning_rate": 0.00018616504854368932, "loss": 0.6773, "step": 120}, {"epoch": 0.23604176123468, "grad_norm": 0.5560890436172485, "learning_rate": 0.00018495145631067963, "loss": 0.6495, "step": 130}, {"epoch": 0.25419881979119385, "grad_norm": 1.0793246030807495, "learning_rate": 0.0001837378640776699, "loss": 0.7041, "step": 140}, {"epoch": 0.2723558783477077, "grad_norm": 0.7616399526596069, "learning_rate": 0.00018252427184466022, "loss": 0.6581, "step": 150}, {"epoch": 0.29051293690422153, "grad_norm": 0.8123521208763123, "learning_rate": 0.0001813106796116505, "loss": 0.6087, "step": 160}, {"epoch": 0.30866999546073537, "grad_norm": 0.8436940312385559, "learning_rate": 0.0001800970873786408, "loss": 0.5235, "step": 170}, {"epoch": 0.3268270540172492, "grad_norm": 0.6446323394775391, "learning_rate": 0.00017888349514563108, "loss": 0.6282, "step": 180}, {"epoch": 0.34498411257376305, "grad_norm": 0.5146214962005615, "learning_rate": 0.0001776699029126214, "loss": 0.6574, "step": 190}, {"epoch": 0.3631411711302769, "grad_norm": 0.3425748944282532, "learning_rate": 0.00017645631067961167, "loss": 0.519, "step": 200}, {"epoch": 0.3812982296867907, "grad_norm": 0.5527833700180054, "learning_rate": 0.00017524271844660195, "loss": 0.5513, "step": 210}, {"epoch": 0.39945528824330456, "grad_norm": 0.44390425086021423, "learning_rate": 0.00017402912621359225, "loss": 0.5954, "step": 220}, {"epoch": 0.41761234679981846, "grad_norm": 0.5045336484909058, "learning_rate": 0.00017281553398058253, "loss": 0.6205, "step": 230}, {"epoch": 0.4357694053563323, "grad_norm": 0.868144690990448, "learning_rate": 0.00017160194174757281, "loss": 0.5752, "step": 240}, {"epoch": 0.45392646391284613, "grad_norm": 0.7955343723297119, "learning_rate": 0.0001703883495145631, "loss": 0.5779, "step": 250}, {"epoch": 0.47208352246936, "grad_norm": 0.9475038051605225, "learning_rate": 0.0001691747572815534, "loss": 0.6078, "step": 260}, {"epoch": 0.4902405810258738, "grad_norm": 0.8108811974525452, "learning_rate": 0.00016796116504854368, "loss": 0.5583, "step": 270}, {"epoch": 0.5083976395823877, "grad_norm": 0.5514959096908569, "learning_rate": 0.000166747572815534, "loss": 0.489, "step": 280}, {"epoch": 0.5265546981389015, "grad_norm": 0.7049546241760254, "learning_rate": 0.00016553398058252427, "loss": 0.5776, "step": 290}, {"epoch": 0.5447117566954154, "grad_norm": 0.5863288640975952, "learning_rate": 0.00016432038834951457, "loss": 0.5303, "step": 300}, {"epoch": 0.5628688152519292, "grad_norm": 0.657037615776062, "learning_rate": 0.00016310679611650485, "loss": 0.5284, "step": 310}, {"epoch": 0.5810258738084431, "grad_norm": 0.8380070924758911, "learning_rate": 0.00016189320388349516, "loss": 0.5437, "step": 320}, {"epoch": 0.5991829323649569, "grad_norm": 0.5513352751731873, "learning_rate": 0.00016067961165048544, "loss": 0.5919, "step": 330}, {"epoch": 0.6173399909214707, "grad_norm": 0.8836792707443237, "learning_rate": 0.00015946601941747575, "loss": 0.5962, "step": 340}, {"epoch": 0.6354970494779846, "grad_norm": 0.9055123329162598, "learning_rate": 0.00015825242718446603, "loss": 0.5814, "step": 350}, {"epoch": 0.6536541080344984, "grad_norm": 0.7191300392150879, "learning_rate": 0.00015703883495145633, "loss": 0.5675, "step": 360}, {"epoch": 0.6718111665910123, "grad_norm": 0.703980028629303, "learning_rate": 0.0001558252427184466, "loss": 0.5527, "step": 370}, {"epoch": 0.6899682251475261, "grad_norm": 1.128692626953125, "learning_rate": 0.00015461165048543692, "loss": 0.5557, "step": 380}, {"epoch": 0.7081252837040399, "grad_norm": 0.8371670842170715, "learning_rate": 0.0001533980582524272, "loss": 0.5075, "step": 390}, {"epoch": 0.7262823422605538, "grad_norm": 1.0714390277862549, "learning_rate": 0.0001521844660194175, "loss": 0.5033, "step": 400}, {"epoch": 0.7444394008170676, "grad_norm": 0.7558730840682983, "learning_rate": 0.00015097087378640778, "loss": 0.4741, "step": 410}, {"epoch": 0.7625964593735814, "grad_norm": 0.7800672054290771, "learning_rate": 0.00014975728155339806, "loss": 0.5557, "step": 420}, {"epoch": 0.7807535179300953, "grad_norm": 0.6719617247581482, "learning_rate": 0.00014854368932038834, "loss": 0.51, "step": 430}, {"epoch": 0.7989105764866091, "grad_norm": 0.9832203984260559, "learning_rate": 0.00014733009708737865, "loss": 0.4867, "step": 440}, {"epoch": 0.817067635043123, "grad_norm": 0.9865148663520813, "learning_rate": 0.00014611650485436893, "loss": 0.4984, "step": 450}, {"epoch": 0.8352246935996369, "grad_norm": 0.5024685859680176, "learning_rate": 0.0001449029126213592, "loss": 0.4833, "step": 460}, {"epoch": 0.8533817521561508, "grad_norm": 0.8082051873207092, "learning_rate": 0.00014368932038834952, "loss": 0.5077, "step": 470}, {"epoch": 0.8715388107126646, "grad_norm": 1.0604666471481323, "learning_rate": 0.0001424757281553398, "loss": 0.492, "step": 480}, {"epoch": 0.8896958692691784, "grad_norm": 0.6536511778831482, "learning_rate": 0.0001412621359223301, "loss": 0.4746, "step": 490}, {"epoch": 0.9078529278256923, "grad_norm": 0.7053747773170471, "learning_rate": 0.00014004854368932038, "loss": 0.4575, "step": 500}, {"epoch": 0.9078529278256923, "eval_loss": 0.47906312346458435, "eval_runtime": 5.854, "eval_samples_per_second": 83.704, "eval_steps_per_second": 41.852, "step": 500}, {"epoch": 0.9260099863822061, "grad_norm": 0.6256751418113708, "learning_rate": 0.0001388349514563107, "loss": 0.474, "step": 510}, {"epoch": 0.94416704493872, "grad_norm": 0.6208598613739014, "learning_rate": 0.00013762135922330097, "loss": 0.4577, "step": 520}, {"epoch": 0.9623241034952338, "grad_norm": 0.3779323399066925, "learning_rate": 0.00013640776699029128, "loss": 0.4391, "step": 530}, {"epoch": 0.9804811620517476, "grad_norm": 0.6622596979141235, "learning_rate": 0.00013519417475728156, "loss": 0.5061, "step": 540}, {"epoch": 0.9986382206082615, "grad_norm": 0.5045062303543091, "learning_rate": 0.00013398058252427186, "loss": 0.5176, "step": 550}, {"epoch": 1.0163413527008625, "grad_norm": 0.7270963788032532, "learning_rate": 0.00013276699029126214, "loss": 0.4625, "step": 560}, {"epoch": 1.0344984112573763, "grad_norm": 0.47354409098625183, "learning_rate": 0.00013155339805825245, "loss": 0.4039, "step": 570}, {"epoch": 1.0526554698138901, "grad_norm": 0.6624945998191833, "learning_rate": 0.00013033980582524273, "loss": 0.4676, "step": 580}, {"epoch": 1.070812528370404, "grad_norm": 0.65047687292099, "learning_rate": 0.00012912621359223304, "loss": 0.4397, "step": 590}, {"epoch": 1.0889695869269178, "grad_norm": 0.9418348670005798, "learning_rate": 0.00012791262135922332, "loss": 0.4177, "step": 600}, {"epoch": 1.1071266454834316, "grad_norm": 0.7053587436676025, "learning_rate": 0.00012669902912621362, "loss": 0.4483, "step": 610}, {"epoch": 1.1252837040399455, "grad_norm": 1.0795843601226807, "learning_rate": 0.0001254854368932039, "loss": 0.4306, "step": 620}, {"epoch": 1.1434407625964593, "grad_norm": 0.8186272978782654, "learning_rate": 0.00012427184466019418, "loss": 0.4852, "step": 630}, {"epoch": 1.1615978211529732, "grad_norm": 0.49790915846824646, "learning_rate": 0.00012305825242718446, "loss": 0.423, "step": 640}, {"epoch": 1.179754879709487, "grad_norm": 0.31377848982810974, "learning_rate": 0.00012184466019417475, "loss": 0.4165, "step": 650}, {"epoch": 1.1979119382660008, "grad_norm": 0.43737128376960754, "learning_rate": 0.00012063106796116506, "loss": 0.4496, "step": 660}, {"epoch": 1.2160689968225147, "grad_norm": 0.8269752860069275, "learning_rate": 0.00011941747572815534, "loss": 0.4532, "step": 670}, {"epoch": 1.2342260553790285, "grad_norm": 0.3955059349536896, "learning_rate": 0.00011820388349514563, "loss": 0.4063, "step": 680}, {"epoch": 1.2523831139355424, "grad_norm": 0.48621928691864014, "learning_rate": 0.00011699029126213593, "loss": 0.3754, "step": 690}, {"epoch": 1.2705401724920562, "grad_norm": 0.6518723368644714, "learning_rate": 0.00011577669902912622, "loss": 0.3964, "step": 700}, {"epoch": 1.28869723104857, "grad_norm": 0.7089611887931824, "learning_rate": 0.0001145631067961165, "loss": 0.4343, "step": 710}, {"epoch": 1.3068542896050839, "grad_norm": 0.5293169617652893, "learning_rate": 0.0001133495145631068, "loss": 0.3999, "step": 720}, {"epoch": 1.3250113481615977, "grad_norm": 0.7710917592048645, "learning_rate": 0.00011213592233009709, "loss": 0.4462, "step": 730}, {"epoch": 1.3431684067181116, "grad_norm": 0.5034338235855103, "learning_rate": 0.00011092233009708739, "loss": 0.4021, "step": 740}, {"epoch": 1.3613254652746254, "grad_norm": 0.5442783236503601, "learning_rate": 0.00010970873786407767, "loss": 0.3896, "step": 750}, {"epoch": 1.3794825238311392, "grad_norm": 0.6952638030052185, "learning_rate": 0.00010849514563106798, "loss": 0.4152, "step": 760}, {"epoch": 1.397639582387653, "grad_norm": 0.526136040687561, "learning_rate": 0.00010728155339805826, "loss": 0.3905, "step": 770}, {"epoch": 1.415796640944167, "grad_norm": 0.40046045184135437, "learning_rate": 0.00010606796116504855, "loss": 0.4176, "step": 780}, {"epoch": 1.433953699500681, "grad_norm": 0.6955530047416687, "learning_rate": 0.00010485436893203883, "loss": 0.4046, "step": 790}, {"epoch": 1.4521107580571948, "grad_norm": 0.535487949848175, "learning_rate": 0.00010364077669902914, "loss": 0.4496, "step": 800}, {"epoch": 1.4702678166137086, "grad_norm": 0.6536657214164734, "learning_rate": 0.00010242718446601942, "loss": 0.4377, "step": 810}, {"epoch": 1.4884248751702225, "grad_norm": 0.7154018878936768, "learning_rate": 0.00010121359223300973, "loss": 0.3934, "step": 820}, {"epoch": 1.5065819337267363, "grad_norm": 0.46300533413887024, "learning_rate": 0.0001, "loss": 0.4512, "step": 830}, {"epoch": 1.5247389922832502, "grad_norm": 0.9148903489112854, "learning_rate": 9.87864077669903e-05, "loss": 0.4497, "step": 840}, {"epoch": 1.542896050839764, "grad_norm": 0.5335402488708496, "learning_rate": 9.757281553398059e-05, "loss": 0.4541, "step": 850}, {"epoch": 1.5610531093962778, "grad_norm": 0.5195198059082031, "learning_rate": 9.635922330097088e-05, "loss": 0.4076, "step": 860}, {"epoch": 1.5792101679527917, "grad_norm": 0.5545899271965027, "learning_rate": 9.514563106796118e-05, "loss": 0.3949, "step": 870}, {"epoch": 1.5973672265093055, "grad_norm": 0.4485359489917755, "learning_rate": 9.393203883495146e-05, "loss": 0.4185, "step": 880}, {"epoch": 1.6155242850658194, "grad_norm": 0.5795057415962219, "learning_rate": 9.271844660194175e-05, "loss": 0.3982, "step": 890}, {"epoch": 1.6336813436223332, "grad_norm": 0.5546781420707703, "learning_rate": 9.150485436893204e-05, "loss": 0.3674, "step": 900}, {"epoch": 1.651838402178847, "grad_norm": 0.3249690532684326, "learning_rate": 9.029126213592234e-05, "loss": 0.3961, "step": 910}, {"epoch": 1.6699954607353609, "grad_norm": 0.5656976103782654, "learning_rate": 8.907766990291263e-05, "loss": 0.4046, "step": 920}, {"epoch": 1.6881525192918747, "grad_norm": 0.6548805236816406, "learning_rate": 8.786407766990292e-05, "loss": 0.442, "step": 930}, {"epoch": 1.7063095778483885, "grad_norm": 0.5105992555618286, "learning_rate": 8.66504854368932e-05, "loss": 0.3893, "step": 940}, {"epoch": 1.7244666364049024, "grad_norm": 0.6542297601699829, "learning_rate": 8.54368932038835e-05, "loss": 0.4241, "step": 950}, {"epoch": 1.7426236949614162, "grad_norm": 0.5902846455574036, "learning_rate": 8.422330097087379e-05, "loss": 0.3958, "step": 960}, {"epoch": 1.76078075351793, "grad_norm": 0.6877330541610718, "learning_rate": 8.300970873786408e-05, "loss": 0.3763, "step": 970}, {"epoch": 1.778937812074444, "grad_norm": 0.46945545077323914, "learning_rate": 8.179611650485438e-05, "loss": 0.4221, "step": 980}, {"epoch": 1.7970948706309577, "grad_norm": 0.46329888701438904, "learning_rate": 8.058252427184466e-05, "loss": 0.4019, "step": 990}, {"epoch": 1.8152519291874716, "grad_norm": 0.6679615378379822, "learning_rate": 7.936893203883495e-05, "loss": 0.3859, "step": 1000}, {"epoch": 1.8152519291874716, "eval_loss": 0.4159320294857025, "eval_runtime": 5.8086, "eval_samples_per_second": 84.358, "eval_steps_per_second": 42.179, "step": 1000}, {"epoch": 1.8334089877439856, "grad_norm": 0.5645595192909241, "learning_rate": 7.815533980582524e-05, "loss": 0.4604, "step": 1010}, {"epoch": 1.8515660463004995, "grad_norm": 0.45632633566856384, "learning_rate": 7.694174757281554e-05, "loss": 0.4092, "step": 1020}, {"epoch": 1.8697231048570133, "grad_norm": 0.5526508092880249, "learning_rate": 7.572815533980583e-05, "loss": 0.389, "step": 1030}, {"epoch": 1.8878801634135272, "grad_norm": 0.5411912202835083, "learning_rate": 7.451456310679612e-05, "loss": 0.4173, "step": 1040}, {"epoch": 1.906037221970041, "grad_norm": 0.3965602517127991, "learning_rate": 7.330097087378641e-05, "loss": 0.4053, "step": 1050}, {"epoch": 1.9241942805265548, "grad_norm": 0.38208499550819397, "learning_rate": 7.208737864077671e-05, "loss": 0.4293, "step": 1060}, {"epoch": 1.9423513390830687, "grad_norm": 0.36381348967552185, "learning_rate": 7.0873786407767e-05, "loss": 0.4117, "step": 1070}, {"epoch": 1.9605083976395825, "grad_norm": 0.5156931281089783, "learning_rate": 6.966019417475728e-05, "loss": 0.3884, "step": 1080}, {"epoch": 1.9786654561960964, "grad_norm": 0.5542136430740356, "learning_rate": 6.844660194174757e-05, "loss": 0.4264, "step": 1090}, {"epoch": 1.9968225147526102, "grad_norm": 0.6285620331764221, "learning_rate": 6.723300970873787e-05, "loss": 0.4487, "step": 1100}, {"epoch": 2.014525646845211, "grad_norm": 0.4408944249153137, "learning_rate": 6.601941747572816e-05, "loss": 0.3686, "step": 1110}, {"epoch": 2.032682705401725, "grad_norm": 0.387673020362854, "learning_rate": 6.480582524271845e-05, "loss": 0.3798, "step": 1120}, {"epoch": 2.0508397639582387, "grad_norm": 0.4596557021141052, "learning_rate": 6.359223300970875e-05, "loss": 0.3588, "step": 1130}, {"epoch": 2.0689968225147526, "grad_norm": 0.5351287126541138, "learning_rate": 6.237864077669903e-05, "loss": 0.3548, "step": 1140}, {"epoch": 2.0871538810712664, "grad_norm": 0.8091621994972229, "learning_rate": 6.116504854368932e-05, "loss": 0.3978, "step": 1150}, {"epoch": 2.1053109396277803, "grad_norm": 0.6877572536468506, "learning_rate": 5.995145631067961e-05, "loss": 0.357, "step": 1160}, {"epoch": 2.123467998184294, "grad_norm": 0.7108643054962158, "learning_rate": 5.87378640776699e-05, "loss": 0.3402, "step": 1170}, {"epoch": 2.141625056740808, "grad_norm": 0.6538242101669312, "learning_rate": 5.752427184466019e-05, "loss": 0.3837, "step": 1180}, {"epoch": 2.159782115297322, "grad_norm": 0.6285730004310608, "learning_rate": 5.6310679611650486e-05, "loss": 0.3456, "step": 1190}, {"epoch": 2.1779391738538356, "grad_norm": 0.5269455909729004, "learning_rate": 5.509708737864078e-05, "loss": 0.3808, "step": 1200}, {"epoch": 2.1960962324103495, "grad_norm": 0.5450138449668884, "learning_rate": 5.3883495145631065e-05, "loss": 0.3576, "step": 1210}, {"epoch": 2.2142532909668633, "grad_norm": 1.0693798065185547, "learning_rate": 5.266990291262136e-05, "loss": 0.3806, "step": 1220}, {"epoch": 2.232410349523377, "grad_norm": 0.7671725749969482, "learning_rate": 5.145631067961165e-05, "loss": 0.3719, "step": 1230}, {"epoch": 2.250567408079891, "grad_norm": 0.7017196416854858, "learning_rate": 5.0242718446601945e-05, "loss": 0.3848, "step": 1240}, {"epoch": 2.268724466636405, "grad_norm": 0.6463766694068909, "learning_rate": 4.902912621359224e-05, "loss": 0.3721, "step": 1250}, {"epoch": 2.2868815251929187, "grad_norm": 0.7198667526245117, "learning_rate": 4.7815533980582525e-05, "loss": 0.349, "step": 1260}, {"epoch": 2.3050385837494325, "grad_norm": 0.6633029580116272, "learning_rate": 4.660194174757282e-05, "loss": 0.3462, "step": 1270}, {"epoch": 2.3231956423059463, "grad_norm": 0.6498831510543823, "learning_rate": 4.538834951456311e-05, "loss": 0.3434, "step": 1280}, {"epoch": 2.34135270086246, "grad_norm": 0.587917685508728, "learning_rate": 4.4174757281553404e-05, "loss": 0.3525, "step": 1290}, {"epoch": 2.359509759418974, "grad_norm": 0.4982687830924988, "learning_rate": 4.296116504854369e-05, "loss": 0.3825, "step": 1300}, {"epoch": 2.377666817975488, "grad_norm": 0.6282124519348145, "learning_rate": 4.1747572815533984e-05, "loss": 0.3776, "step": 1310}, {"epoch": 2.3958238765320017, "grad_norm": 0.5797330141067505, "learning_rate": 4.053398058252427e-05, "loss": 0.3587, "step": 1320}, {"epoch": 2.4139809350885155, "grad_norm": 0.47264063358306885, "learning_rate": 3.9320388349514564e-05, "loss": 0.3612, "step": 1330}, {"epoch": 2.4321379936450294, "grad_norm": 0.8647959232330322, "learning_rate": 3.810679611650486e-05, "loss": 0.3702, "step": 1340}, {"epoch": 2.450295052201543, "grad_norm": 0.6579964756965637, "learning_rate": 3.689320388349515e-05, "loss": 0.4142, "step": 1350}, {"epoch": 2.468452110758057, "grad_norm": 0.574661910533905, "learning_rate": 3.5679611650485437e-05, "loss": 0.3447, "step": 1360}, {"epoch": 2.486609169314571, "grad_norm": 0.8757120370864868, "learning_rate": 3.446601941747573e-05, "loss": 0.4084, "step": 1370}, {"epoch": 2.5047662278710847, "grad_norm": 0.6891030669212341, "learning_rate": 3.325242718446602e-05, "loss": 0.3469, "step": 1380}, {"epoch": 2.5229232864275986, "grad_norm": 0.6934592127799988, "learning_rate": 3.2038834951456316e-05, "loss": 0.3774, "step": 1390}, {"epoch": 2.5410803449841124, "grad_norm": 0.6309433579444885, "learning_rate": 3.08252427184466e-05, "loss": 0.3491, "step": 1400}, {"epoch": 2.5592374035406262, "grad_norm": 0.5958977341651917, "learning_rate": 2.9611650485436892e-05, "loss": 0.3927, "step": 1410}, {"epoch": 2.57739446209714, "grad_norm": 0.6955950856208801, "learning_rate": 2.8398058252427186e-05, "loss": 0.3792, "step": 1420}, {"epoch": 2.595551520653654, "grad_norm": 0.5683951377868652, "learning_rate": 2.7184466019417475e-05, "loss": 0.3697, "step": 1430}, {"epoch": 2.6137085792101677, "grad_norm": 0.7483524680137634, "learning_rate": 2.597087378640777e-05, "loss": 0.3437, "step": 1440}, {"epoch": 2.6318656377666816, "grad_norm": 0.6225605010986328, "learning_rate": 2.475728155339806e-05, "loss": 0.356, "step": 1450}, {"epoch": 2.6500226963231954, "grad_norm": 0.6200382113456726, "learning_rate": 2.3543689320388352e-05, "loss": 0.3575, "step": 1460}, {"epoch": 2.6681797548797093, "grad_norm": 0.5318754315376282, "learning_rate": 2.233009708737864e-05, "loss": 0.3419, "step": 1470}, {"epoch": 2.686336813436223, "grad_norm": 0.5362626910209656, "learning_rate": 2.111650485436893e-05, "loss": 0.3418, "step": 1480}, {"epoch": 2.704493871992737, "grad_norm": 0.6648288369178772, "learning_rate": 1.9902912621359225e-05, "loss": 0.3548, "step": 1490}, {"epoch": 2.722650930549251, "grad_norm": 0.5095891356468201, "learning_rate": 1.8689320388349514e-05, "loss": 0.3941, "step": 1500}, {"epoch": 2.722650930549251, "eval_loss": 0.38953956961631775, "eval_runtime": 5.8359, "eval_samples_per_second": 83.963, "eval_steps_per_second": 41.981, "step": 1500}, {"epoch": 2.7408079891057646, "grad_norm": 0.7666894793510437, "learning_rate": 1.7475728155339808e-05, "loss": 0.3336, "step": 1510}, {"epoch": 2.7589650476622785, "grad_norm": 0.5626208782196045, "learning_rate": 1.6262135922330097e-05, "loss": 0.3628, "step": 1520}, {"epoch": 2.7771221062187923, "grad_norm": 0.4624424874782562, "learning_rate": 1.5048543689320387e-05, "loss": 0.364, "step": 1530}, {"epoch": 2.795279164775306, "grad_norm": 0.6420198082923889, "learning_rate": 1.383495145631068e-05, "loss": 0.3625, "step": 1540}, {"epoch": 2.81343622333182, "grad_norm": 0.7124646902084351, "learning_rate": 1.2621359223300972e-05, "loss": 0.3295, "step": 1550}, {"epoch": 2.831593281888334, "grad_norm": 0.7917710542678833, "learning_rate": 1.1407766990291263e-05, "loss": 0.344, "step": 1560}, {"epoch": 2.849750340444848, "grad_norm": 0.7078279256820679, "learning_rate": 1.0194174757281553e-05, "loss": 0.3792, "step": 1570}, {"epoch": 2.867907399001362, "grad_norm": 0.5565842986106873, "learning_rate": 8.980582524271845e-06, "loss": 0.3414, "step": 1580}, {"epoch": 2.8860644575578758, "grad_norm": 0.8108804821968079, "learning_rate": 7.766990291262136e-06, "loss": 0.3397, "step": 1590}, {"epoch": 2.9042215161143896, "grad_norm": 0.6310741305351257, "learning_rate": 6.553398058252427e-06, "loss": 0.3526, "step": 1600}, {"epoch": 2.9223785746709035, "grad_norm": 0.7984017729759216, "learning_rate": 5.3398058252427185e-06, "loss": 0.3289, "step": 1610}, {"epoch": 2.9405356332274173, "grad_norm": 0.6530895829200745, "learning_rate": 4.12621359223301e-06, "loss": 0.3851, "step": 1620}, {"epoch": 2.958692691783931, "grad_norm": 0.49926266074180603, "learning_rate": 2.912621359223301e-06, "loss": 0.3141, "step": 1630}, {"epoch": 2.976849750340445, "grad_norm": 0.5100964903831482, "learning_rate": 1.6990291262135922e-06, "loss": 0.3557, "step": 1640}, {"epoch": 2.995006808896959, "grad_norm": 0.7602570652961731, "learning_rate": 4.854368932038835e-07, "loss": 0.3714, "step": 1650}], "logging_steps": 10, "max_steps": 1653, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 7883146962640896.0, "train_batch_size": 2, "trial_name": null, "trial_params": null}