#!/usr/bin/env python3
"""
Centralized Dataset Management System for BGL and Thunderbird Log Classification

This module provides a unified interface for creating, loading, and managing
train/test datasets for both BGL and Thunderbird log classification tasks.

Key Features:
- Fixed train/test splits of 10,000 samples each
- No data leakage between train and test sets
- Support for using smaller subsets during development
- Consistent data format across both datasets
- Reproducible splits with fixed random seeds
"""

import os
import json
import random
import pickle
from typing import List, Tuple, Dict, Optional
from pathlib import Path


class DatasetConfig:
    """Configuration for dataset management"""
    
    # Dataset paths
    BGL_DATASET_PATH = "datasets/BGL/BGL.log"
    THUNDERBIRD_DATASET_PATH = "datasets/Thunderbird/Thunderbird_10M.log"
    
    # Fixed dataset sizes
    TRAIN_SIZE = 10000
    TEST_SIZE = 10000
    TOTAL_SAMPLES = TRAIN_SIZE + TEST_SIZE  # 20,000 total
    
    # Output directories for saved datasets
    DATASETS_OUTPUT_DIR = "prepared_datasets"
    BGL_OUTPUT_DIR = os.path.join(DATASETS_OUTPUT_DIR, "BGL")
    THUNDERBIRD_OUTPUT_DIR = os.path.join(DATASETS_OUTPUT_DIR, "Thunderbird")
    
    # Random seed for reproducible splits
    RANDOM_SEED = 42


def parse_log_line(line: str, dataset_type: str = "BGL") -> Tuple[str, str]:
    """
    Parse a log line and extract the label and content
    
    Args:
        line: Raw log line
        dataset_type: Either "BGL" or "Thunderbird" (affects parsing logic)
        
    Returns:
        Tuple of (label, clean_content) where label is 'normal' or 'anomaly'
        and clean_content has all label markers removed
    """
    line = line.strip()
    if line.startswith('-'):
        label = 'normal'
        # Remove the leading "- " and extract the actual log content
        content = line[2:] if len(line) > 2 else line
    else:
        label = 'anomaly'
        # For anomaly logs, remove the prefix to get clean content
        # Split on first space and take everything after the first token
        parts = line.split(' ', 1)
        content = parts[1] if len(parts) > 1 else line
    
    return label, content


def create_fixed_dataset_split(dataset_path: str, dataset_type: str, 
                             total_samples: int = DatasetConfig.TOTAL_SAMPLES,
                             train_size: int = DatasetConfig.TRAIN_SIZE,
                             test_size: int = DatasetConfig.TEST_SIZE,
                             random_seed: int = DatasetConfig.RANDOM_SEED) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
    """
    Create a fixed train-test split by sampling line numbers first
    
    Args:
        dataset_path: Path to the log file
        dataset_type: Either "BGL" or "Thunderbird"
        total_samples: Total number of samples to extract
        train_size: Number of training samples
        test_size: Number of test samples
        random_seed: Random seed for reproducible splits
        
    Returns:
        Tuple of (train_samples, test_samples)
    """
    print(f"Creating fixed {dataset_type} dataset split...")
    print(f"Target: {train_size} train + {test_size} test = {total_samples} total samples")
    
    # Set random seed for reproducibility
    random.seed(random_seed)
    
    # First, count total lines
    with open(dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
        total_lines = sum(1 for _ in f)
    
    print(f"Total lines in {dataset_type} dataset: {total_lines:,}")
    
    if total_samples > total_lines:
        print(f"WARNING: Requested {total_samples} samples but only {total_lines} lines available")
        total_samples = total_lines
        # Adjust train/test sizes proportionally
        train_size = int(total_samples * 0.5)
        test_size = total_samples - train_size
        print(f"Adjusted to: {train_size} train + {test_size} test = {total_samples} total")
    
    # Sample line numbers randomly
    sampled_line_numbers = sorted(random.sample(range(total_lines), total_samples))
    print(f"Sampled {len(sampled_line_numbers)} line numbers")

    # Convert to set for O(1) lookup performance
    sampled_line_numbers_set = set(sampled_line_numbers)
    print(f"Reading sampled lines from dataset...")

    # Read the sampled lines
    samples = []
    with open(dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
        for line_idx, line in enumerate(f):
            if line_idx in sampled_line_numbers_set:
                label, content = parse_log_line(line, dataset_type)
                if content.strip():  # Only include non-empty content
                    samples.append((label, content.strip()))

                # Progress indicator for large datasets
                if len(samples) % 1000 == 0:
                    print(f"  Loaded {len(samples)}/{total_samples} samples...")

                # Early exit if we have all samples
                if len(samples) >= total_samples:
                    break
    
    print(f"Successfully loaded {len(samples)} samples")
    
    # Shuffle the samples before splitting
    random.shuffle(samples)
    
    # Split into train and test
    train_samples = samples[:train_size]
    test_samples = samples[train_size:train_size + test_size]
    
    # Print distribution information
    def print_distribution(samples, split_name):
        normal_count = sum(1 for label, _ in samples if label == 'normal')
        anomaly_count = len(samples) - normal_count
        anomaly_rate = (anomaly_count / len(samples)) * 100 if samples else 0
        print(f"  {split_name}: {len(samples)} samples - {normal_count} normal, {anomaly_count} anomaly ({anomaly_rate:.1f}%)")
    
    print(f"\n{dataset_type} Dataset Distribution:")
    print_distribution(train_samples, "Train")
    print_distribution(test_samples, "Test")
    
    return train_samples, test_samples


def save_dataset_split(train_samples: List[Tuple[str, str]], 
                      test_samples: List[Tuple[str, str]], 
                      output_dir: str, 
                      dataset_type: str):
    """
    Save train and test samples to files
    
    Args:
        train_samples: Training samples
        test_samples: Test samples  
        output_dir: Output directory
        dataset_type: Dataset type for logging
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Save as JSON for human readability
    train_path = os.path.join(output_dir, "train_samples.json")
    test_path = os.path.join(output_dir, "test_samples.json")
    
    with open(train_path, "w") as f:
        json.dump(train_samples, f, indent=2)
    
    with open(test_path, "w") as f:
        json.dump(test_samples, f, indent=2)
    
    # Also save as pickle for faster loading
    train_pickle_path = os.path.join(output_dir, "train_samples.pkl")
    test_pickle_path = os.path.join(output_dir, "test_samples.pkl")
    
    with open(train_pickle_path, "wb") as f:
        pickle.dump(train_samples, f)
    
    with open(test_pickle_path, "wb") as f:
        pickle.dump(test_samples, f)
    
    # Save metadata
    metadata = {
        "dataset_type": dataset_type,
        "train_size": len(train_samples),
        "test_size": len(test_samples),
        "total_size": len(train_samples) + len(test_samples),
        "random_seed": DatasetConfig.RANDOM_SEED,
        "created_at": str(Path().cwd()),
        "train_distribution": {
            "normal": sum(1 for label, _ in train_samples if label == 'normal'),
            "anomaly": sum(1 for label, _ in train_samples if label == 'anomaly')
        },
        "test_distribution": {
            "normal": sum(1 for label, _ in test_samples if label == 'normal'),
            "anomaly": sum(1 for label, _ in test_samples if label == 'anomaly')
        }
    }
    
    metadata_path = os.path.join(output_dir, "metadata.json")
    with open(metadata_path, "w") as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\n{dataset_type} dataset saved to {output_dir}:")
    print(f"  - {train_path}")
    print(f"  - {test_path}")
    print(f"  - {train_pickle_path}")
    print(f"  - {test_pickle_path}")
    print(f"  - {metadata_path}")


def load_dataset_split(dataset_dir: str, use_pickle: bool = True) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
    """
    Load train and test samples from saved files
    
    Args:
        dataset_dir: Directory containing the saved dataset
        use_pickle: Whether to use pickle files for faster loading
        
    Returns:
        Tuple of (train_samples, test_samples)
    """
    if use_pickle:
        train_path = os.path.join(dataset_dir, "train_samples.pkl")
        test_path = os.path.join(dataset_dir, "test_samples.pkl")
        
        if os.path.exists(train_path) and os.path.exists(test_path):
            with open(train_path, "rb") as f:
                train_samples = pickle.load(f)
            with open(test_path, "rb") as f:
                test_samples = pickle.load(f)
            return train_samples, test_samples
    
    # Fallback to JSON
    train_path = os.path.join(dataset_dir, "train_samples.json")
    test_path = os.path.join(dataset_dir, "test_samples.json")
    
    if not (os.path.exists(train_path) and os.path.exists(test_path)):
        raise FileNotFoundError(f"Dataset files not found in {dataset_dir}")
    
    with open(train_path, "r") as f:
        train_samples = json.load(f)
    with open(test_path, "r") as f:
        test_samples = json.load(f)
    
    return train_samples, test_samples


def get_dataset_subset(samples: List[Tuple[str, str]], max_size: Optional[int] = None,
                      random_seed: int = DatasetConfig.RANDOM_SEED) -> List[Tuple[str, str]]:
    """
    Get a subset of samples for development/testing with smaller datasets

    Args:
        samples: Full list of samples
        max_size: Maximum number of samples to return (None = return all)
        random_seed: Random seed for reproducible subsets

    Returns:
        Subset of samples
    """
    if max_size is None or max_size >= len(samples):
        return samples

    # Set random seed for reproducible subsets
    random.seed(random_seed)
    return random.sample(samples, max_size)


def load_metadata(dataset_dir: str) -> Dict:
    """
    Load dataset metadata

    Args:
        dataset_dir: Directory containing the dataset

    Returns:
        Metadata dictionary
    """
    metadata_path = os.path.join(dataset_dir, "metadata.json")
    if not os.path.exists(metadata_path):
        raise FileNotFoundError(f"Metadata file not found: {metadata_path}")

    with open(metadata_path, "r") as f:
        return json.load(f)


def print_dataset_info(dataset_dir: str):
    """
    Print information about a saved dataset

    Args:
        dataset_dir: Directory containing the dataset
    """
    try:
        metadata = load_metadata(dataset_dir)
        print(f"\nDataset Information: {metadata['dataset_type']}")
        print("=" * 50)
        print(f"Total samples: {metadata['total_size']:,}")
        print(f"Train samples: {metadata['train_size']:,}")
        print(f"Test samples: {metadata['test_size']:,}")
        print(f"Random seed: {metadata['random_seed']}")

        print(f"\nTrain Distribution:")
        train_dist = metadata['train_distribution']
        train_total = train_dist['normal'] + train_dist['anomaly']
        print(f"  Normal: {train_dist['normal']:,} ({train_dist['normal']/train_total*100:.1f}%)")
        print(f"  Anomaly: {train_dist['anomaly']:,} ({train_dist['anomaly']/train_total*100:.1f}%)")

        print(f"\nTest Distribution:")
        test_dist = metadata['test_distribution']
        test_total = test_dist['normal'] + test_dist['anomaly']
        print(f"  Normal: {test_dist['normal']:,} ({test_dist['normal']/test_total*100:.1f}%)")
        print(f"  Anomaly: {test_dist['anomaly']:,} ({test_dist['anomaly']/test_total*100:.1f}%)")

    except FileNotFoundError as e:
        print(f"Error: {e}")


class DatasetManager:
    """
    High-level interface for dataset management
    """

    def __init__(self):
        self.config = DatasetConfig()

    def prepare_bgl_dataset(self, force_recreate: bool = False) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
        """
        Prepare BGL dataset (create if not exists, load if exists)

        Args:
            force_recreate: Whether to recreate the dataset even if it exists

        Returns:
            Tuple of (train_samples, test_samples)
        """
        if not force_recreate and os.path.exists(self.config.BGL_OUTPUT_DIR):
            print("Loading existing BGL dataset...")
            return load_dataset_split(self.config.BGL_OUTPUT_DIR)

        print("Creating new BGL dataset...")
        train_samples, test_samples = create_fixed_dataset_split(
            self.config.BGL_DATASET_PATH,
            "BGL"
        )
        save_dataset_split(train_samples, test_samples, self.config.BGL_OUTPUT_DIR, "BGL")
        return train_samples, test_samples

    def prepare_thunderbird_dataset(self, force_recreate: bool = False) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
        """
        Prepare Thunderbird dataset (create if not exists, load if exists)

        Args:
            force_recreate: Whether to recreate the dataset even if it exists

        Returns:
            Tuple of (train_samples, test_samples)
        """
        if not force_recreate and os.path.exists(self.config.THUNDERBIRD_OUTPUT_DIR):
            print("Loading existing Thunderbird dataset...")
            return load_dataset_split(self.config.THUNDERBIRD_OUTPUT_DIR)

        print("Creating new Thunderbird dataset...")
        train_samples, test_samples = create_fixed_dataset_split(
            self.config.THUNDERBIRD_DATASET_PATH,
            "Thunderbird"
        )
        save_dataset_split(train_samples, test_samples, self.config.THUNDERBIRD_OUTPUT_DIR, "Thunderbird")
        return train_samples, test_samples

    def get_bgl_data(self, train_size: Optional[int] = None, test_size: Optional[int] = None) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
        """
        Get BGL data with optional size limits

        Args:
            train_size: Maximum training samples (None = use all)
            test_size: Maximum test samples (None = use all)

        Returns:
            Tuple of (train_samples, test_samples)
        """
        train_samples, test_samples = self.prepare_bgl_dataset()

        if train_size is not None:
            train_samples = get_dataset_subset(train_samples, train_size)
        if test_size is not None:
            test_samples = get_dataset_subset(test_samples, test_size)

        return train_samples, test_samples

    def get_thunderbird_data(self, train_size: Optional[int] = None, test_size: Optional[int] = None) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
        """
        Get Thunderbird data with optional size limits

        Args:
            train_size: Maximum training samples (None = use all)
            test_size: Maximum test samples (None = use all)

        Returns:
            Tuple of (train_samples, test_samples)
        """
        train_samples, test_samples = self.prepare_thunderbird_dataset()

        if train_size is not None:
            train_samples = get_dataset_subset(train_samples, train_size)
        if test_size is not None:
            test_samples = get_dataset_subset(test_samples, test_size)

        return train_samples, test_samples

    def print_all_dataset_info(self):
        """Print information about all available datasets"""
        print("Available Datasets:")
        print("=" * 60)

        if os.path.exists(self.config.BGL_OUTPUT_DIR):
            print_dataset_info(self.config.BGL_OUTPUT_DIR)
        else:
            print("\nBGL Dataset: Not prepared yet")

        if os.path.exists(self.config.THUNDERBIRD_OUTPUT_DIR):
            print_dataset_info(self.config.THUNDERBIRD_OUTPUT_DIR)
        else:
            print("\nThunderbird Dataset: Not prepared yet")


# Convenience functions for backward compatibility
def get_bgl_train_test_data(train_size: Optional[int] = None, test_size: Optional[int] = None) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
    """Convenience function to get BGL train/test data"""
    manager = DatasetManager()
    return manager.get_bgl_data(train_size, test_size)


def get_thunderbird_train_test_data(train_size: Optional[int] = None, test_size: Optional[int] = None) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
    """Convenience function to get Thunderbird train/test data"""
    manager = DatasetManager()
    return manager.get_thunderbird_data(train_size, test_size)
