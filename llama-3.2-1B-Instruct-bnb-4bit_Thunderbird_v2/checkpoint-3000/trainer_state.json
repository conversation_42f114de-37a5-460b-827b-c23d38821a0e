{"best_global_step": 3000, "best_metric": 0.35742640495300293, "best_model_checkpoint": "./llama-3.2-1B-Instruct-bnb-4bit_Thunderbird_v2/checkpoint-3000", "epoch": 2.6666666666666665, "eval_steps": 500, "global_step": 3000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.008888888888888889, "grad_norm": 1.4907935857772827, "learning_rate": 0.00019976261127596442, "loss": 3.7307, "step": 10}, {"epoch": 0.017777777777777778, "grad_norm": 1.3174653053283691, "learning_rate": 0.0001991691394658754, "loss": 2.0411, "step": 20}, {"epoch": 0.02666666666666667, "grad_norm": 1.1771109104156494, "learning_rate": 0.00019857566765578636, "loss": 1.5122, "step": 30}, {"epoch": 0.035555555555555556, "grad_norm": 1.2547985315322876, "learning_rate": 0.00019798219584569736, "loss": 1.2616, "step": 40}, {"epoch": 0.044444444444444446, "grad_norm": 1.5969797372817993, "learning_rate": 0.0001973887240356083, "loss": 0.8882, "step": 50}, {"epoch": 0.05333333333333334, "grad_norm": 0.9260269403457642, "learning_rate": 0.0001967952522255193, "loss": 0.7182, "step": 60}, {"epoch": 0.06222222222222222, "grad_norm": 0.9886362552642822, "learning_rate": 0.00019620178041543026, "loss": 0.7402, "step": 70}, {"epoch": 0.07111111111111111, "grad_norm": 1.1009612083435059, "learning_rate": 0.00019560830860534126, "loss": 0.8216, "step": 80}, {"epoch": 0.08, "grad_norm": 0.6347482204437256, "learning_rate": 0.00019501483679525223, "loss": 0.6868, "step": 90}, {"epoch": 0.08888888888888889, "grad_norm": 1.2444019317626953, "learning_rate": 0.0001944213649851632, "loss": 0.6823, "step": 100}, {"epoch": 0.09777777777777778, "grad_norm": 0.6797393560409546, "learning_rate": 0.0001938278931750742, "loss": 0.6271, "step": 110}, {"epoch": 0.10666666666666667, "grad_norm": 1.2095094919204712, "learning_rate": 0.00019323442136498517, "loss": 0.6699, "step": 120}, {"epoch": 0.11555555555555555, "grad_norm": 1.00873863697052, "learning_rate": 0.00019264094955489616, "loss": 0.6392, "step": 130}, {"epoch": 0.12444444444444444, "grad_norm": 0.8059144616127014, "learning_rate": 0.00019204747774480713, "loss": 0.5677, "step": 140}, {"epoch": 0.13333333333333333, "grad_norm": 0.9507359862327576, "learning_rate": 0.0001914540059347181, "loss": 0.6768, "step": 150}, {"epoch": 0.14222222222222222, "grad_norm": 0.9997856616973877, "learning_rate": 0.0001908605341246291, "loss": 0.6499, "step": 160}, {"epoch": 0.1511111111111111, "grad_norm": 0.9377870559692383, "learning_rate": 0.00019026706231454007, "loss": 0.7355, "step": 170}, {"epoch": 0.16, "grad_norm": 0.7828674912452698, "learning_rate": 0.00018967359050445104, "loss": 0.5964, "step": 180}, {"epoch": 0.1688888888888889, "grad_norm": 0.6360064148902893, "learning_rate": 0.000189080118694362, "loss": 0.4967, "step": 190}, {"epoch": 0.17777777777777778, "grad_norm": 0.6222010850906372, "learning_rate": 0.000188486646884273, "loss": 0.7392, "step": 200}, {"epoch": 0.18666666666666668, "grad_norm": 0.4854641556739807, "learning_rate": 0.00018789317507418398, "loss": 0.6085, "step": 210}, {"epoch": 0.19555555555555557, "grad_norm": 0.5587230324745178, "learning_rate": 0.00018729970326409497, "loss": 0.6194, "step": 220}, {"epoch": 0.20444444444444446, "grad_norm": 0.6662908792495728, "learning_rate": 0.00018670623145400594, "loss": 0.5893, "step": 230}, {"epoch": 0.21333333333333335, "grad_norm": 0.9255284667015076, "learning_rate": 0.0001861127596439169, "loss": 0.5274, "step": 240}, {"epoch": 0.2222222222222222, "grad_norm": 0.819394052028656, "learning_rate": 0.0001855192878338279, "loss": 0.6058, "step": 250}, {"epoch": 0.2311111111111111, "grad_norm": 0.6764024496078491, "learning_rate": 0.00018492581602373888, "loss": 0.5661, "step": 260}, {"epoch": 0.24, "grad_norm": 1.081036925315857, "learning_rate": 0.00018433234421364988, "loss": 0.5919, "step": 270}, {"epoch": 0.24888888888888888, "grad_norm": 0.6027657985687256, "learning_rate": 0.00018373887240356085, "loss": 0.5195, "step": 280}, {"epoch": 0.2577777777777778, "grad_norm": 0.9639933109283447, "learning_rate": 0.00018314540059347181, "loss": 0.6045, "step": 290}, {"epoch": 0.26666666666666666, "grad_norm": 0.8439876437187195, "learning_rate": 0.0001825519287833828, "loss": 0.6901, "step": 300}, {"epoch": 0.27555555555555555, "grad_norm": 0.5846402049064636, "learning_rate": 0.00018195845697329375, "loss": 0.5146, "step": 310}, {"epoch": 0.28444444444444444, "grad_norm": 0.6951016783714294, "learning_rate": 0.00018136498516320475, "loss": 0.5524, "step": 320}, {"epoch": 0.29333333333333333, "grad_norm": 0.5409045219421387, "learning_rate": 0.00018077151335311572, "loss": 0.5417, "step": 330}, {"epoch": 0.3022222222222222, "grad_norm": 0.9187454581260681, "learning_rate": 0.00018017804154302672, "loss": 0.5032, "step": 340}, {"epoch": 0.3111111111111111, "grad_norm": 0.44607511162757874, "learning_rate": 0.0001795845697329377, "loss": 0.4654, "step": 350}, {"epoch": 0.32, "grad_norm": 1.1955041885375977, "learning_rate": 0.00017899109792284868, "loss": 0.5937, "step": 360}, {"epoch": 0.3288888888888889, "grad_norm": 0.8485992550849915, "learning_rate": 0.00017839762611275965, "loss": 0.5446, "step": 370}, {"epoch": 0.3377777777777778, "grad_norm": 0.4120846092700958, "learning_rate": 0.00017780415430267062, "loss": 0.5062, "step": 380}, {"epoch": 0.3466666666666667, "grad_norm": 0.9192973971366882, "learning_rate": 0.00017721068249258162, "loss": 0.5341, "step": 390}, {"epoch": 0.35555555555555557, "grad_norm": 0.4690617620944977, "learning_rate": 0.0001766172106824926, "loss": 0.4771, "step": 400}, {"epoch": 0.36444444444444446, "grad_norm": 0.5153684020042419, "learning_rate": 0.0001760237388724036, "loss": 0.5214, "step": 410}, {"epoch": 0.37333333333333335, "grad_norm": 0.8027042150497437, "learning_rate": 0.00017543026706231456, "loss": 0.4697, "step": 420}, {"epoch": 0.38222222222222224, "grad_norm": 0.6567326188087463, "learning_rate": 0.00017483679525222553, "loss": 0.4463, "step": 430}, {"epoch": 0.39111111111111113, "grad_norm": 0.623389482498169, "learning_rate": 0.0001742433234421365, "loss": 0.4794, "step": 440}, {"epoch": 0.4, "grad_norm": 0.7382842302322388, "learning_rate": 0.00017364985163204747, "loss": 0.5172, "step": 450}, {"epoch": 0.4088888888888889, "grad_norm": 0.9401490688323975, "learning_rate": 0.00017305637982195846, "loss": 0.5508, "step": 460}, {"epoch": 0.4177777777777778, "grad_norm": 0.8688564300537109, "learning_rate": 0.00017246290801186943, "loss": 0.4841, "step": 470}, {"epoch": 0.4266666666666667, "grad_norm": 0.867922842502594, "learning_rate": 0.00017186943620178043, "loss": 0.5628, "step": 480}, {"epoch": 0.43555555555555553, "grad_norm": 0.7917380928993225, "learning_rate": 0.0001712759643916914, "loss": 0.51, "step": 490}, {"epoch": 0.4444444444444444, "grad_norm": 0.6370946764945984, "learning_rate": 0.0001706824925816024, "loss": 0.4144, "step": 500}, {"epoch": 0.4444444444444444, "eval_loss": 0.4803629219532013, "eval_runtime": 11.4021, "eval_samples_per_second": 87.703, "eval_steps_per_second": 43.852, "step": 500}, {"epoch": 0.4533333333333333, "grad_norm": 0.6847711801528931, "learning_rate": 0.00017008902077151337, "loss": 0.4694, "step": 510}, {"epoch": 0.4622222222222222, "grad_norm": 0.6198802590370178, "learning_rate": 0.00016949554896142434, "loss": 0.4656, "step": 520}, {"epoch": 0.4711111111111111, "grad_norm": 0.718302309513092, "learning_rate": 0.00016890207715133533, "loss": 0.4829, "step": 530}, {"epoch": 0.48, "grad_norm": 0.697076678276062, "learning_rate": 0.0001683086053412463, "loss": 0.4699, "step": 540}, {"epoch": 0.4888888888888889, "grad_norm": 0.6036356091499329, "learning_rate": 0.0001677151335311573, "loss": 0.4892, "step": 550}, {"epoch": 0.49777777777777776, "grad_norm": 0.7722182869911194, "learning_rate": 0.00016712166172106827, "loss": 0.4639, "step": 560}, {"epoch": 0.5066666666666667, "grad_norm": 0.49047142267227173, "learning_rate": 0.00016652818991097924, "loss": 0.4759, "step": 570}, {"epoch": 0.5155555555555555, "grad_norm": 0.6210607290267944, "learning_rate": 0.0001659347181008902, "loss": 0.4306, "step": 580}, {"epoch": 0.5244444444444445, "grad_norm": 0.5255789160728455, "learning_rate": 0.00016534124629080118, "loss": 0.4345, "step": 590}, {"epoch": 0.5333333333333333, "grad_norm": 0.5862236618995667, "learning_rate": 0.00016474777448071217, "loss": 0.4784, "step": 600}, {"epoch": 0.5422222222222223, "grad_norm": 0.5979678630828857, "learning_rate": 0.00016415430267062314, "loss": 0.4512, "step": 610}, {"epoch": 0.5511111111111111, "grad_norm": 0.6097372770309448, "learning_rate": 0.00016356083086053414, "loss": 0.4678, "step": 620}, {"epoch": 0.56, "grad_norm": 0.482791930437088, "learning_rate": 0.0001629673590504451, "loss": 0.4562, "step": 630}, {"epoch": 0.5688888888888889, "grad_norm": 0.7428238391876221, "learning_rate": 0.00016237388724035608, "loss": 0.4515, "step": 640}, {"epoch": 0.5777777777777777, "grad_norm": 0.5615348815917969, "learning_rate": 0.00016178041543026708, "loss": 0.5067, "step": 650}, {"epoch": 0.5866666666666667, "grad_norm": 0.6662471890449524, "learning_rate": 0.00016118694362017805, "loss": 0.4657, "step": 660}, {"epoch": 0.5955555555555555, "grad_norm": 0.40482261776924133, "learning_rate": 0.00016059347181008904, "loss": 0.4376, "step": 670}, {"epoch": 0.6044444444444445, "grad_norm": 0.5849538445472717, "learning_rate": 0.00016, "loss": 0.4826, "step": 680}, {"epoch": 0.6133333333333333, "grad_norm": 0.40357571840286255, "learning_rate": 0.000159406528189911, "loss": 0.4876, "step": 690}, {"epoch": 0.6222222222222222, "grad_norm": 0.834804892539978, "learning_rate": 0.00015881305637982195, "loss": 0.4226, "step": 700}, {"epoch": 0.6311111111111111, "grad_norm": 0.89620041847229, "learning_rate": 0.00015821958456973295, "loss": 0.4108, "step": 710}, {"epoch": 0.64, "grad_norm": 1.135987401008606, "learning_rate": 0.00015762611275964392, "loss": 0.4888, "step": 720}, {"epoch": 0.6488888888888888, "grad_norm": 0.5398806929588318, "learning_rate": 0.0001570326409495549, "loss": 0.4379, "step": 730}, {"epoch": 0.6577777777777778, "grad_norm": 0.5907015800476074, "learning_rate": 0.00015643916913946589, "loss": 0.4576, "step": 740}, {"epoch": 0.6666666666666666, "grad_norm": 0.5421211123466492, "learning_rate": 0.00015584569732937686, "loss": 0.4614, "step": 750}, {"epoch": 0.6755555555555556, "grad_norm": 0.5390458106994629, "learning_rate": 0.00015525222551928785, "loss": 0.4508, "step": 760}, {"epoch": 0.6844444444444444, "grad_norm": 1.0648486614227295, "learning_rate": 0.00015465875370919882, "loss": 0.3994, "step": 770}, {"epoch": 0.6933333333333334, "grad_norm": 0.3651842772960663, "learning_rate": 0.0001540652818991098, "loss": 0.4075, "step": 780}, {"epoch": 0.7022222222222222, "grad_norm": 0.40261295437812805, "learning_rate": 0.0001534718100890208, "loss": 0.461, "step": 790}, {"epoch": 0.7111111111111111, "grad_norm": 0.4837977886199951, "learning_rate": 0.00015287833827893176, "loss": 0.4498, "step": 800}, {"epoch": 0.72, "grad_norm": 0.5420933365821838, "learning_rate": 0.00015228486646884275, "loss": 0.4453, "step": 810}, {"epoch": 0.7288888888888889, "grad_norm": 0.5964518785476685, "learning_rate": 0.00015169139465875372, "loss": 0.4431, "step": 820}, {"epoch": 0.7377777777777778, "grad_norm": 0.48211827874183655, "learning_rate": 0.0001510979228486647, "loss": 0.4164, "step": 830}, {"epoch": 0.7466666666666667, "grad_norm": 0.6013233065605164, "learning_rate": 0.00015050445103857566, "loss": 0.3668, "step": 840}, {"epoch": 0.7555555555555555, "grad_norm": 0.5600693225860596, "learning_rate": 0.00014991097922848666, "loss": 0.449, "step": 850}, {"epoch": 0.7644444444444445, "grad_norm": 0.38672322034835815, "learning_rate": 0.00014931750741839763, "loss": 0.4071, "step": 860}, {"epoch": 0.7733333333333333, "grad_norm": 0.8080921173095703, "learning_rate": 0.0001487240356083086, "loss": 0.4349, "step": 870}, {"epoch": 0.7822222222222223, "grad_norm": 0.5061087012290955, "learning_rate": 0.0001481305637982196, "loss": 0.4215, "step": 880}, {"epoch": 0.7911111111111111, "grad_norm": 0.505233645439148, "learning_rate": 0.00014753709198813057, "loss": 0.4124, "step": 890}, {"epoch": 0.8, "grad_norm": 0.42028501629829407, "learning_rate": 0.00014694362017804156, "loss": 0.3584, "step": 900}, {"epoch": 0.8088888888888889, "grad_norm": 0.3916836977005005, "learning_rate": 0.00014635014836795253, "loss": 0.4115, "step": 910}, {"epoch": 0.8177777777777778, "grad_norm": 0.675207257270813, "learning_rate": 0.0001457566765578635, "loss": 0.4348, "step": 920}, {"epoch": 0.8266666666666667, "grad_norm": 0.5196228623390198, "learning_rate": 0.0001451632047477745, "loss": 0.3931, "step": 930}, {"epoch": 0.8355555555555556, "grad_norm": 0.48566707968711853, "learning_rate": 0.00014456973293768547, "loss": 0.4175, "step": 940}, {"epoch": 0.8444444444444444, "grad_norm": 0.6151915192604065, "learning_rate": 0.00014397626112759647, "loss": 0.4227, "step": 950}, {"epoch": 0.8533333333333334, "grad_norm": 0.5665645599365234, "learning_rate": 0.0001433827893175074, "loss": 0.417, "step": 960}, {"epoch": 0.8622222222222222, "grad_norm": 0.4918266832828522, "learning_rate": 0.0001427893175074184, "loss": 0.4013, "step": 970}, {"epoch": 0.8711111111111111, "grad_norm": 0.5335206985473633, "learning_rate": 0.00014219584569732938, "loss": 0.4082, "step": 980}, {"epoch": 0.88, "grad_norm": 0.4288569390773773, "learning_rate": 0.00014160237388724035, "loss": 0.3855, "step": 990}, {"epoch": 0.8888888888888888, "grad_norm": 0.5072354078292847, "learning_rate": 0.00014100890207715134, "loss": 0.4285, "step": 1000}, {"epoch": 0.8888888888888888, "eval_loss": 0.4258759021759033, "eval_runtime": 11.3162, "eval_samples_per_second": 88.369, "eval_steps_per_second": 44.185, "step": 1000}, {"epoch": 0.8977777777777778, "grad_norm": 0.31542378664016724, "learning_rate": 0.0001404154302670623, "loss": 0.4033, "step": 1010}, {"epoch": 0.9066666666666666, "grad_norm": 0.3285101354122162, "learning_rate": 0.0001398219584569733, "loss": 0.4157, "step": 1020}, {"epoch": 0.9155555555555556, "grad_norm": 0.5085433721542358, "learning_rate": 0.00013922848664688428, "loss": 0.3944, "step": 1030}, {"epoch": 0.9244444444444444, "grad_norm": 1.3388787508010864, "learning_rate": 0.00013863501483679527, "loss": 0.4141, "step": 1040}, {"epoch": 0.9333333333333333, "grad_norm": 0.5208550095558167, "learning_rate": 0.00013804154302670624, "loss": 0.4379, "step": 1050}, {"epoch": 0.9422222222222222, "grad_norm": 0.6014845967292786, "learning_rate": 0.00013744807121661721, "loss": 0.4146, "step": 1060}, {"epoch": 0.9511111111111111, "grad_norm": 0.5735152959823608, "learning_rate": 0.0001368545994065282, "loss": 0.3464, "step": 1070}, {"epoch": 0.96, "grad_norm": 0.3791601061820984, "learning_rate": 0.00013626112759643918, "loss": 0.3915, "step": 1080}, {"epoch": 0.9688888888888889, "grad_norm": 0.5021987557411194, "learning_rate": 0.00013566765578635015, "loss": 0.4022, "step": 1090}, {"epoch": 0.9777777777777777, "grad_norm": 0.5006941556930542, "learning_rate": 0.00013507418397626112, "loss": 0.3608, "step": 1100}, {"epoch": 0.9866666666666667, "grad_norm": 0.33929017186164856, "learning_rate": 0.00013448071216617212, "loss": 0.4177, "step": 1110}, {"epoch": 0.9955555555555555, "grad_norm": 0.4738145172595978, "learning_rate": 0.0001338872403560831, "loss": 0.4492, "step": 1120}, {"epoch": 1.0044444444444445, "grad_norm": 0.41086170077323914, "learning_rate": 0.00013329376854599406, "loss": 0.3654, "step": 1130}, {"epoch": 1.0133333333333334, "grad_norm": 0.5441796183586121, "learning_rate": 0.00013270029673590505, "loss": 0.3654, "step": 1140}, {"epoch": 1.0222222222222221, "grad_norm": 0.5154232382774353, "learning_rate": 0.00013210682492581602, "loss": 0.3797, "step": 1150}, {"epoch": 1.031111111111111, "grad_norm": 0.48260390758514404, "learning_rate": 0.00013151335311572702, "loss": 0.4115, "step": 1160}, {"epoch": 1.04, "grad_norm": 0.5127752423286438, "learning_rate": 0.000130919881305638, "loss": 0.3591, "step": 1170}, {"epoch": 1.048888888888889, "grad_norm": 0.4086529016494751, "learning_rate": 0.00013032640949554899, "loss": 0.3877, "step": 1180}, {"epoch": 1.0577777777777777, "grad_norm": 0.585506021976471, "learning_rate": 0.00012973293768545996, "loss": 0.3735, "step": 1190}, {"epoch": 1.0666666666666667, "grad_norm": 0.4751863479614258, "learning_rate": 0.00012913946587537093, "loss": 0.3551, "step": 1200}, {"epoch": 1.0755555555555556, "grad_norm": 0.527491569519043, "learning_rate": 0.00012854599406528192, "loss": 0.3313, "step": 1210}, {"epoch": 1.0844444444444445, "grad_norm": 0.6983230113983154, "learning_rate": 0.00012795252225519287, "loss": 0.4109, "step": 1220}, {"epoch": 1.0933333333333333, "grad_norm": 0.5651285648345947, "learning_rate": 0.00012735905044510386, "loss": 0.3629, "step": 1230}, {"epoch": 1.1022222222222222, "grad_norm": 0.6592049598693848, "learning_rate": 0.00012676557863501483, "loss": 0.4087, "step": 1240}, {"epoch": 1.1111111111111112, "grad_norm": 0.54755699634552, "learning_rate": 0.00012617210682492583, "loss": 0.4108, "step": 1250}, {"epoch": 1.12, "grad_norm": 0.6140095591545105, "learning_rate": 0.0001255786350148368, "loss": 0.3713, "step": 1260}, {"epoch": 1.1288888888888888, "grad_norm": 0.4957113265991211, "learning_rate": 0.00012498516320474777, "loss": 0.3674, "step": 1270}, {"epoch": 1.1377777777777778, "grad_norm": 0.4401528537273407, "learning_rate": 0.00012439169139465876, "loss": 0.3911, "step": 1280}, {"epoch": 1.1466666666666667, "grad_norm": 0.5259028077125549, "learning_rate": 0.00012379821958456973, "loss": 0.3901, "step": 1290}, {"epoch": 1.1555555555555554, "grad_norm": 0.49322187900543213, "learning_rate": 0.00012320474777448073, "loss": 0.3749, "step": 1300}, {"epoch": 1.1644444444444444, "grad_norm": 0.4116743505001068, "learning_rate": 0.0001226112759643917, "loss": 0.3713, "step": 1310}, {"epoch": 1.1733333333333333, "grad_norm": 0.42254212498664856, "learning_rate": 0.00012201780415430268, "loss": 0.3423, "step": 1320}, {"epoch": 1.1822222222222223, "grad_norm": 0.39367127418518066, "learning_rate": 0.00012142433234421367, "loss": 0.3908, "step": 1330}, {"epoch": 1.1911111111111112, "grad_norm": 0.3033267855644226, "learning_rate": 0.00012083086053412465, "loss": 0.3385, "step": 1340}, {"epoch": 1.2, "grad_norm": 0.38957977294921875, "learning_rate": 0.00012023738872403561, "loss": 0.3533, "step": 1350}, {"epoch": 1.208888888888889, "grad_norm": 0.5424385070800781, "learning_rate": 0.00011964391691394659, "loss": 0.4201, "step": 1360}, {"epoch": 1.2177777777777778, "grad_norm": 0.507588267326355, "learning_rate": 0.00011905044510385756, "loss": 0.3933, "step": 1370}, {"epoch": 1.2266666666666666, "grad_norm": 0.7076835632324219, "learning_rate": 0.00011845697329376854, "loss": 0.3906, "step": 1380}, {"epoch": 1.2355555555555555, "grad_norm": 0.5769990086555481, "learning_rate": 0.00011786350148367953, "loss": 0.3803, "step": 1390}, {"epoch": 1.2444444444444445, "grad_norm": 0.782890796661377, "learning_rate": 0.00011727002967359051, "loss": 0.3865, "step": 1400}, {"epoch": 1.2533333333333334, "grad_norm": 0.8180720806121826, "learning_rate": 0.00011667655786350149, "loss": 0.3856, "step": 1410}, {"epoch": 1.2622222222222224, "grad_norm": 0.7307953238487244, "learning_rate": 0.00011608308605341248, "loss": 0.4234, "step": 1420}, {"epoch": 1.271111111111111, "grad_norm": 0.5944843292236328, "learning_rate": 0.00011548961424332345, "loss": 0.3532, "step": 1430}, {"epoch": 1.28, "grad_norm": 0.5009840130805969, "learning_rate": 0.00011489614243323443, "loss": 0.3313, "step": 1440}, {"epoch": 1.2888888888888888, "grad_norm": 0.6714699864387512, "learning_rate": 0.00011430267062314541, "loss": 0.3823, "step": 1450}, {"epoch": 1.2977777777777777, "grad_norm": 0.5084232687950134, "learning_rate": 0.0001137091988130564, "loss": 0.3594, "step": 1460}, {"epoch": 1.3066666666666666, "grad_norm": 0.4875299334526062, "learning_rate": 0.00011311572700296738, "loss": 0.401, "step": 1470}, {"epoch": 1.3155555555555556, "grad_norm": 0.3907798230648041, "learning_rate": 0.00011252225519287834, "loss": 0.3535, "step": 1480}, {"epoch": 1.3244444444444445, "grad_norm": 0.6875836849212646, "learning_rate": 0.00011192878338278932, "loss": 0.3654, "step": 1490}, {"epoch": 1.3333333333333333, "grad_norm": 0.4832308292388916, "learning_rate": 0.0001113353115727003, "loss": 0.41, "step": 1500}, {"epoch": 1.3333333333333333, "eval_loss": 0.39691638946533203, "eval_runtime": 11.0445, "eval_samples_per_second": 90.543, "eval_steps_per_second": 45.271, "step": 1500}, {"epoch": 1.3422222222222222, "grad_norm": 0.45819005370140076, "learning_rate": 0.00011074183976261127, "loss": 0.3827, "step": 1510}, {"epoch": 1.3511111111111112, "grad_norm": 0.5933734178543091, "learning_rate": 0.00011014836795252225, "loss": 0.3495, "step": 1520}, {"epoch": 1.3599999999999999, "grad_norm": 0.45033034682273865, "learning_rate": 0.00010955489614243324, "loss": 0.3559, "step": 1530}, {"epoch": 1.3688888888888888, "grad_norm": 0.6011955738067627, "learning_rate": 0.00010896142433234422, "loss": 0.3854, "step": 1540}, {"epoch": 1.3777777777777778, "grad_norm": 0.46560704708099365, "learning_rate": 0.0001083679525222552, "loss": 0.328, "step": 1550}, {"epoch": 1.3866666666666667, "grad_norm": 0.5286521315574646, "learning_rate": 0.00010777448071216619, "loss": 0.4053, "step": 1560}, {"epoch": 1.3955555555555557, "grad_norm": 0.5339436531066895, "learning_rate": 0.00010718100890207716, "loss": 0.3715, "step": 1570}, {"epoch": 1.4044444444444444, "grad_norm": 0.6306799054145813, "learning_rate": 0.00010658753709198814, "loss": 0.3608, "step": 1580}, {"epoch": 1.4133333333333333, "grad_norm": 0.5093494057655334, "learning_rate": 0.00010599406528189912, "loss": 0.3604, "step": 1590}, {"epoch": 1.4222222222222223, "grad_norm": 0.26636990904808044, "learning_rate": 0.00010540059347181011, "loss": 0.352, "step": 1600}, {"epoch": 1.431111111111111, "grad_norm": 0.5071695446968079, "learning_rate": 0.00010480712166172106, "loss": 0.347, "step": 1610}, {"epoch": 1.44, "grad_norm": 0.5080844759941101, "learning_rate": 0.00010421364985163205, "loss": 0.3727, "step": 1620}, {"epoch": 1.448888888888889, "grad_norm": 0.3753253221511841, "learning_rate": 0.00010362017804154303, "loss": 0.3734, "step": 1630}, {"epoch": 1.4577777777777778, "grad_norm": 0.6203786134719849, "learning_rate": 0.00010302670623145401, "loss": 0.3865, "step": 1640}, {"epoch": 1.4666666666666668, "grad_norm": 0.5736299753189087, "learning_rate": 0.00010243323442136498, "loss": 0.358, "step": 1650}, {"epoch": 1.4755555555555555, "grad_norm": 0.6886458992958069, "learning_rate": 0.00010183976261127597, "loss": 0.3594, "step": 1660}, {"epoch": 1.4844444444444445, "grad_norm": 0.5221061110496521, "learning_rate": 0.00010124629080118695, "loss": 0.3786, "step": 1670}, {"epoch": 1.4933333333333334, "grad_norm": 0.6066006422042847, "learning_rate": 0.00010065281899109793, "loss": 0.3619, "step": 1680}, {"epoch": 1.5022222222222221, "grad_norm": 0.44401729106903076, "learning_rate": 0.00010005934718100892, "loss": 0.3375, "step": 1690}, {"epoch": 1.511111111111111, "grad_norm": 0.550075352191925, "learning_rate": 9.946587537091989e-05, "loss": 0.3662, "step": 1700}, {"epoch": 1.52, "grad_norm": 0.4240083694458008, "learning_rate": 9.887240356083086e-05, "loss": 0.3756, "step": 1710}, {"epoch": 1.528888888888889, "grad_norm": 0.5515788197517395, "learning_rate": 9.827893175074184e-05, "loss": 0.3707, "step": 1720}, {"epoch": 1.537777777777778, "grad_norm": 0.5399845838546753, "learning_rate": 9.768545994065282e-05, "loss": 0.3514, "step": 1730}, {"epoch": 1.5466666666666666, "grad_norm": 0.47854140400886536, "learning_rate": 9.70919881305638e-05, "loss": 0.3746, "step": 1740}, {"epoch": 1.5555555555555556, "grad_norm": 0.891587495803833, "learning_rate": 9.649851632047479e-05, "loss": 0.3734, "step": 1750}, {"epoch": 1.5644444444444443, "grad_norm": 0.5421565175056458, "learning_rate": 9.590504451038577e-05, "loss": 0.3815, "step": 1760}, {"epoch": 1.5733333333333333, "grad_norm": 0.5347034931182861, "learning_rate": 9.531157270029674e-05, "loss": 0.3614, "step": 1770}, {"epoch": 1.5822222222222222, "grad_norm": 0.47696012258529663, "learning_rate": 9.471810089020771e-05, "loss": 0.396, "step": 1780}, {"epoch": 1.5911111111111111, "grad_norm": 0.5680587887763977, "learning_rate": 9.41246290801187e-05, "loss": 0.3867, "step": 1790}, {"epoch": 1.6, "grad_norm": 0.7064895033836365, "learning_rate": 9.353115727002968e-05, "loss": 0.3746, "step": 1800}, {"epoch": 1.608888888888889, "grad_norm": 0.4865037202835083, "learning_rate": 9.293768545994066e-05, "loss": 0.3518, "step": 1810}, {"epoch": 1.6177777777777778, "grad_norm": 0.5085668563842773, "learning_rate": 9.234421364985164e-05, "loss": 0.3448, "step": 1820}, {"epoch": 1.6266666666666667, "grad_norm": 0.43879827857017517, "learning_rate": 9.175074183976261e-05, "loss": 0.3798, "step": 1830}, {"epoch": 1.6355555555555554, "grad_norm": 0.3981216251850128, "learning_rate": 9.11572700296736e-05, "loss": 0.3493, "step": 1840}, {"epoch": 1.6444444444444444, "grad_norm": 0.4884190261363983, "learning_rate": 9.056379821958457e-05, "loss": 0.3668, "step": 1850}, {"epoch": 1.6533333333333333, "grad_norm": 0.5554038882255554, "learning_rate": 8.997032640949555e-05, "loss": 0.329, "step": 1860}, {"epoch": 1.6622222222222223, "grad_norm": 0.40075454115867615, "learning_rate": 8.937685459940653e-05, "loss": 0.3782, "step": 1870}, {"epoch": 1.6711111111111112, "grad_norm": 0.40097203850746155, "learning_rate": 8.878338278931752e-05, "loss": 0.3685, "step": 1880}, {"epoch": 1.6800000000000002, "grad_norm": 0.3640095889568329, "learning_rate": 8.81899109792285e-05, "loss": 0.3723, "step": 1890}, {"epoch": 1.6888888888888889, "grad_norm": 0.5547574162483215, "learning_rate": 8.759643916913947e-05, "loss": 0.3813, "step": 1900}, {"epoch": 1.6977777777777778, "grad_norm": 0.6672824621200562, "learning_rate": 8.700296735905045e-05, "loss": 0.3927, "step": 1910}, {"epoch": 1.7066666666666666, "grad_norm": 0.5056165456771851, "learning_rate": 8.640949554896142e-05, "loss": 0.4006, "step": 1920}, {"epoch": 1.7155555555555555, "grad_norm": 0.4339386522769928, "learning_rate": 8.58160237388724e-05, "loss": 0.3741, "step": 1930}, {"epoch": 1.7244444444444444, "grad_norm": 0.4526037275791168, "learning_rate": 8.522255192878339e-05, "loss": 0.3942, "step": 1940}, {"epoch": 1.7333333333333334, "grad_norm": 0.4354988634586334, "learning_rate": 8.462908011869437e-05, "loss": 0.3322, "step": 1950}, {"epoch": 1.7422222222222223, "grad_norm": 0.5851741433143616, "learning_rate": 8.403560830860534e-05, "loss": 0.3591, "step": 1960}, {"epoch": 1.751111111111111, "grad_norm": 0.4864458739757538, "learning_rate": 8.344213649851633e-05, "loss": 0.3815, "step": 1970}, {"epoch": 1.76, "grad_norm": 0.379988431930542, "learning_rate": 8.284866468842731e-05, "loss": 0.3681, "step": 1980}, {"epoch": 1.7688888888888887, "grad_norm": 0.5246033072471619, "learning_rate": 8.225519287833828e-05, "loss": 0.3511, "step": 1990}, {"epoch": 1.7777777777777777, "grad_norm": 0.5998423099517822, "learning_rate": 8.166172106824926e-05, "loss": 0.3668, "step": 2000}, {"epoch": 1.7777777777777777, "eval_loss": 0.37604954838752747, "eval_runtime": 11.1878, "eval_samples_per_second": 89.383, "eval_steps_per_second": 44.692, "step": 2000}, {"epoch": 1.7866666666666666, "grad_norm": 0.3697780966758728, "learning_rate": 8.106824925816024e-05, "loss": 0.3393, "step": 2010}, {"epoch": 1.7955555555555556, "grad_norm": 0.4444706439971924, "learning_rate": 8.047477744807123e-05, "loss": 0.3111, "step": 2020}, {"epoch": 1.8044444444444445, "grad_norm": 0.5841779112815857, "learning_rate": 7.98813056379822e-05, "loss": 0.3662, "step": 2030}, {"epoch": 1.8133333333333335, "grad_norm": 0.41711190342903137, "learning_rate": 7.928783382789318e-05, "loss": 0.3251, "step": 2040}, {"epoch": 1.8222222222222222, "grad_norm": 0.36008909344673157, "learning_rate": 7.869436201780415e-05, "loss": 0.3551, "step": 2050}, {"epoch": 1.8311111111111111, "grad_norm": 0.4401097893714905, "learning_rate": 7.810089020771513e-05, "loss": 0.3287, "step": 2060}, {"epoch": 1.8399999999999999, "grad_norm": 0.553989827632904, "learning_rate": 7.750741839762612e-05, "loss": 0.3722, "step": 2070}, {"epoch": 1.8488888888888888, "grad_norm": 0.44408732652664185, "learning_rate": 7.69139465875371e-05, "loss": 0.3472, "step": 2080}, {"epoch": 1.8577777777777778, "grad_norm": 0.42729848623275757, "learning_rate": 7.632047477744807e-05, "loss": 0.3201, "step": 2090}, {"epoch": 1.8666666666666667, "grad_norm": 0.49215853214263916, "learning_rate": 7.572700296735905e-05, "loss": 0.3539, "step": 2100}, {"epoch": 1.8755555555555556, "grad_norm": 0.34798410534858704, "learning_rate": 7.513353115727004e-05, "loss": 0.3402, "step": 2110}, {"epoch": 1.8844444444444446, "grad_norm": 0.4962211549282074, "learning_rate": 7.4540059347181e-05, "loss": 0.3457, "step": 2120}, {"epoch": 1.8933333333333333, "grad_norm": 0.5370779633522034, "learning_rate": 7.394658753709199e-05, "loss": 0.3506, "step": 2130}, {"epoch": 1.9022222222222223, "grad_norm": 0.6861307621002197, "learning_rate": 7.335311572700297e-05, "loss": 0.3481, "step": 2140}, {"epoch": 1.911111111111111, "grad_norm": 0.5135952234268188, "learning_rate": 7.275964391691396e-05, "loss": 0.3818, "step": 2150}, {"epoch": 1.92, "grad_norm": 0.585800290107727, "learning_rate": 7.216617210682493e-05, "loss": 0.354, "step": 2160}, {"epoch": 1.9288888888888889, "grad_norm": 0.4567185938358307, "learning_rate": 7.157270029673591e-05, "loss": 0.3564, "step": 2170}, {"epoch": 1.9377777777777778, "grad_norm": 0.4794982373714447, "learning_rate": 7.097922848664689e-05, "loss": 0.3859, "step": 2180}, {"epoch": 1.9466666666666668, "grad_norm": 0.46491822600364685, "learning_rate": 7.038575667655786e-05, "loss": 0.3743, "step": 2190}, {"epoch": 1.9555555555555557, "grad_norm": 0.485914021730423, "learning_rate": 6.979228486646885e-05, "loss": 0.3358, "step": 2200}, {"epoch": 1.9644444444444444, "grad_norm": 0.44177207350730896, "learning_rate": 6.919881305637983e-05, "loss": 0.3753, "step": 2210}, {"epoch": 1.9733333333333334, "grad_norm": 0.36331093311309814, "learning_rate": 6.86053412462908e-05, "loss": 0.3486, "step": 2220}, {"epoch": 1.982222222222222, "grad_norm": 0.4101469814777374, "learning_rate": 6.801186943620178e-05, "loss": 0.3065, "step": 2230}, {"epoch": 1.991111111111111, "grad_norm": 0.3206250071525574, "learning_rate": 6.741839762611276e-05, "loss": 0.335, "step": 2240}, {"epoch": 2.0, "grad_norm": 0.3111404776573181, "learning_rate": 6.682492581602375e-05, "loss": 0.315, "step": 2250}, {"epoch": 2.008888888888889, "grad_norm": 0.43449923396110535, "learning_rate": 6.623145400593472e-05, "loss": 0.311, "step": 2260}, {"epoch": 2.017777777777778, "grad_norm": 0.6805900931358337, "learning_rate": 6.56379821958457e-05, "loss": 0.3119, "step": 2270}, {"epoch": 2.026666666666667, "grad_norm": 0.4898533821105957, "learning_rate": 6.504451038575668e-05, "loss": 0.3119, "step": 2280}, {"epoch": 2.0355555555555553, "grad_norm": 0.482461154460907, "learning_rate": 6.445103857566765e-05, "loss": 0.3126, "step": 2290}, {"epoch": 2.0444444444444443, "grad_norm": 0.49420401453971863, "learning_rate": 6.385756676557864e-05, "loss": 0.3295, "step": 2300}, {"epoch": 2.0533333333333332, "grad_norm": 0.503478467464447, "learning_rate": 6.326409495548962e-05, "loss": 0.3377, "step": 2310}, {"epoch": 2.062222222222222, "grad_norm": 0.3836277425289154, "learning_rate": 6.26706231454006e-05, "loss": 0.3149, "step": 2320}, {"epoch": 2.071111111111111, "grad_norm": 0.6823098063468933, "learning_rate": 6.207715133531157e-05, "loss": 0.3327, "step": 2330}, {"epoch": 2.08, "grad_norm": 0.622216522693634, "learning_rate": 6.148367952522256e-05, "loss": 0.3464, "step": 2340}, {"epoch": 2.088888888888889, "grad_norm": 0.6148738265037537, "learning_rate": 6.089020771513353e-05, "loss": 0.3279, "step": 2350}, {"epoch": 2.097777777777778, "grad_norm": 0.4441947937011719, "learning_rate": 6.029673590504451e-05, "loss": 0.325, "step": 2360}, {"epoch": 2.1066666666666665, "grad_norm": 0.4336749017238617, "learning_rate": 5.970326409495549e-05, "loss": 0.3238, "step": 2370}, {"epoch": 2.1155555555555554, "grad_norm": 0.6752058267593384, "learning_rate": 5.910979228486647e-05, "loss": 0.3303, "step": 2380}, {"epoch": 2.1244444444444444, "grad_norm": 0.6480968594551086, "learning_rate": 5.851632047477745e-05, "loss": 0.3026, "step": 2390}, {"epoch": 2.1333333333333333, "grad_norm": 0.7174631357192993, "learning_rate": 5.7922848664688436e-05, "loss": 0.3542, "step": 2400}, {"epoch": 2.1422222222222222, "grad_norm": 0.5508649945259094, "learning_rate": 5.732937685459941e-05, "loss": 0.3339, "step": 2410}, {"epoch": 2.151111111111111, "grad_norm": 0.755305826663971, "learning_rate": 5.673590504451038e-05, "loss": 0.3465, "step": 2420}, {"epoch": 2.16, "grad_norm": 0.6186630725860596, "learning_rate": 5.6142433234421366e-05, "loss": 0.3291, "step": 2430}, {"epoch": 2.168888888888889, "grad_norm": 0.4837929904460907, "learning_rate": 5.554896142433235e-05, "loss": 0.3492, "step": 2440}, {"epoch": 2.1777777777777776, "grad_norm": 0.47830259799957275, "learning_rate": 5.4955489614243325e-05, "loss": 0.3182, "step": 2450}, {"epoch": 2.1866666666666665, "grad_norm": 0.7014247179031372, "learning_rate": 5.436201780415431e-05, "loss": 0.3245, "step": 2460}, {"epoch": 2.1955555555555555, "grad_norm": 1.0795059204101562, "learning_rate": 5.376854599406529e-05, "loss": 0.3498, "step": 2470}, {"epoch": 2.2044444444444444, "grad_norm": 0.6410087943077087, "learning_rate": 5.317507418397626e-05, "loss": 0.3421, "step": 2480}, {"epoch": 2.2133333333333334, "grad_norm": 0.6319717764854431, "learning_rate": 5.258160237388724e-05, "loss": 0.3295, "step": 2490}, {"epoch": 2.2222222222222223, "grad_norm": 0.6013391017913818, "learning_rate": 5.198813056379822e-05, "loss": 0.347, "step": 2500}, {"epoch": 2.2222222222222223, "eval_loss": 0.3668886721134186, "eval_runtime": 11.3289, "eval_samples_per_second": 88.27, "eval_steps_per_second": 44.135, "step": 2500}, {"epoch": 2.2311111111111113, "grad_norm": 0.5474236607551575, "learning_rate": 5.1394658753709205e-05, "loss": 0.3513, "step": 2510}, {"epoch": 2.24, "grad_norm": 0.4379067122936249, "learning_rate": 5.080118694362018e-05, "loss": 0.3195, "step": 2520}, {"epoch": 2.2488888888888887, "grad_norm": 0.6245419979095459, "learning_rate": 5.0207715133531164e-05, "loss": 0.3246, "step": 2530}, {"epoch": 2.2577777777777777, "grad_norm": 0.6291339993476868, "learning_rate": 4.961424332344214e-05, "loss": 0.3677, "step": 2540}, {"epoch": 2.2666666666666666, "grad_norm": 0.6590089201927185, "learning_rate": 4.902077151335312e-05, "loss": 0.3354, "step": 2550}, {"epoch": 2.2755555555555556, "grad_norm": 0.6407197117805481, "learning_rate": 4.8427299703264094e-05, "loss": 0.3339, "step": 2560}, {"epoch": 2.2844444444444445, "grad_norm": 0.5517929196357727, "learning_rate": 4.783382789317508e-05, "loss": 0.3184, "step": 2570}, {"epoch": 2.2933333333333334, "grad_norm": 0.6695407629013062, "learning_rate": 4.724035608308606e-05, "loss": 0.3029, "step": 2580}, {"epoch": 2.3022222222222224, "grad_norm": 0.7063927054405212, "learning_rate": 4.664688427299703e-05, "loss": 0.3105, "step": 2590}, {"epoch": 2.311111111111111, "grad_norm": 0.5436565279960632, "learning_rate": 4.605341246290801e-05, "loss": 0.3409, "step": 2600}, {"epoch": 2.32, "grad_norm": 0.6071383953094482, "learning_rate": 4.5459940652818997e-05, "loss": 0.3404, "step": 2610}, {"epoch": 2.328888888888889, "grad_norm": 0.5635979175567627, "learning_rate": 4.486646884272997e-05, "loss": 0.3009, "step": 2620}, {"epoch": 2.3377777777777777, "grad_norm": 0.4830688238143921, "learning_rate": 4.427299703264095e-05, "loss": 0.3351, "step": 2630}, {"epoch": 2.3466666666666667, "grad_norm": 0.4484807252883911, "learning_rate": 4.367952522255193e-05, "loss": 0.3218, "step": 2640}, {"epoch": 2.3555555555555556, "grad_norm": 0.5566081404685974, "learning_rate": 4.308605341246291e-05, "loss": 0.2904, "step": 2650}, {"epoch": 2.3644444444444446, "grad_norm": 0.6881074905395508, "learning_rate": 4.2492581602373886e-05, "loss": 0.311, "step": 2660}, {"epoch": 2.3733333333333335, "grad_norm": 0.612821102142334, "learning_rate": 4.189910979228487e-05, "loss": 0.3129, "step": 2670}, {"epoch": 2.3822222222222225, "grad_norm": 0.5315420627593994, "learning_rate": 4.130563798219585e-05, "loss": 0.2893, "step": 2680}, {"epoch": 2.391111111111111, "grad_norm": 0.47797051072120667, "learning_rate": 4.071216617210682e-05, "loss": 0.3154, "step": 2690}, {"epoch": 2.4, "grad_norm": 0.5300828218460083, "learning_rate": 4.0118694362017805e-05, "loss": 0.2906, "step": 2700}, {"epoch": 2.408888888888889, "grad_norm": 0.4186369478702545, "learning_rate": 3.952522255192879e-05, "loss": 0.3111, "step": 2710}, {"epoch": 2.417777777777778, "grad_norm": 0.8673467636108398, "learning_rate": 3.8931750741839765e-05, "loss": 0.3319, "step": 2720}, {"epoch": 2.4266666666666667, "grad_norm": 0.562703013420105, "learning_rate": 3.833827893175074e-05, "loss": 0.3284, "step": 2730}, {"epoch": 2.4355555555555557, "grad_norm": 0.41841113567352295, "learning_rate": 3.7744807121661725e-05, "loss": 0.3068, "step": 2740}, {"epoch": 2.4444444444444446, "grad_norm": 0.6124343872070312, "learning_rate": 3.715133531157271e-05, "loss": 0.3281, "step": 2750}, {"epoch": 2.453333333333333, "grad_norm": 0.8824100494384766, "learning_rate": 3.655786350148368e-05, "loss": 0.3223, "step": 2760}, {"epoch": 2.462222222222222, "grad_norm": 0.6264799237251282, "learning_rate": 3.596439169139466e-05, "loss": 0.3466, "step": 2770}, {"epoch": 2.471111111111111, "grad_norm": 0.5709545612335205, "learning_rate": 3.5370919881305644e-05, "loss": 0.3501, "step": 2780}, {"epoch": 2.48, "grad_norm": 0.4574767053127289, "learning_rate": 3.4777448071216614e-05, "loss": 0.3088, "step": 2790}, {"epoch": 2.488888888888889, "grad_norm": 0.4686577320098877, "learning_rate": 3.41839762611276e-05, "loss": 0.3189, "step": 2800}, {"epoch": 2.497777777777778, "grad_norm": 0.5467737913131714, "learning_rate": 3.359050445103858e-05, "loss": 0.334, "step": 2810}, {"epoch": 2.506666666666667, "grad_norm": 0.5771108865737915, "learning_rate": 3.299703264094956e-05, "loss": 0.3141, "step": 2820}, {"epoch": 2.5155555555555553, "grad_norm": 0.5735198855400085, "learning_rate": 3.2403560830860533e-05, "loss": 0.3284, "step": 2830}, {"epoch": 2.5244444444444447, "grad_norm": 0.3913235366344452, "learning_rate": 3.181008902077152e-05, "loss": 0.3369, "step": 2840}, {"epoch": 2.533333333333333, "grad_norm": 0.5105829238891602, "learning_rate": 3.121661721068249e-05, "loss": 0.3095, "step": 2850}, {"epoch": 2.542222222222222, "grad_norm": 0.561847984790802, "learning_rate": 3.062314540059347e-05, "loss": 0.3198, "step": 2860}, {"epoch": 2.551111111111111, "grad_norm": 0.5120994448661804, "learning_rate": 3.0029673590504453e-05, "loss": 0.3029, "step": 2870}, {"epoch": 2.56, "grad_norm": 0.5697465538978577, "learning_rate": 2.9436201780415433e-05, "loss": 0.3558, "step": 2880}, {"epoch": 2.568888888888889, "grad_norm": 0.8690246939659119, "learning_rate": 2.884272997032641e-05, "loss": 0.2912, "step": 2890}, {"epoch": 2.5777777777777775, "grad_norm": 0.6485334038734436, "learning_rate": 2.824925816023739e-05, "loss": 0.3421, "step": 2900}, {"epoch": 2.586666666666667, "grad_norm": 0.8800995349884033, "learning_rate": 2.7655786350148372e-05, "loss": 0.3209, "step": 2910}, {"epoch": 2.5955555555555554, "grad_norm": 0.5655707120895386, "learning_rate": 2.7062314540059346e-05, "loss": 0.3359, "step": 2920}, {"epoch": 2.6044444444444443, "grad_norm": 0.590397834777832, "learning_rate": 2.646884272997033e-05, "loss": 0.3215, "step": 2930}, {"epoch": 2.6133333333333333, "grad_norm": 0.44896960258483887, "learning_rate": 2.587537091988131e-05, "loss": 0.3031, "step": 2940}, {"epoch": 2.6222222222222222, "grad_norm": 0.5668341517448425, "learning_rate": 2.5281899109792285e-05, "loss": 0.3146, "step": 2950}, {"epoch": 2.631111111111111, "grad_norm": 0.5110942125320435, "learning_rate": 2.4688427299703265e-05, "loss": 0.3159, "step": 2960}, {"epoch": 2.64, "grad_norm": 0.554351270198822, "learning_rate": 2.409495548961424e-05, "loss": 0.3279, "step": 2970}, {"epoch": 2.648888888888889, "grad_norm": 0.5237232446670532, "learning_rate": 2.3501483679525225e-05, "loss": 0.3058, "step": 2980}, {"epoch": 2.6577777777777776, "grad_norm": 0.5378047227859497, "learning_rate": 2.29080118694362e-05, "loss": 0.3173, "step": 2990}, {"epoch": 2.6666666666666665, "grad_norm": 0.6271426677703857, "learning_rate": 2.2314540059347185e-05, "loss": 0.3545, "step": 3000}, {"epoch": 2.6666666666666665, "eval_loss": 0.35742640495300293, "eval_runtime": 11.6322, "eval_samples_per_second": 85.968, "eval_steps_per_second": 42.984, "step": 3000}], "logging_steps": 10, "max_steps": 3375, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.4412450995503104e+16, "train_batch_size": 2, "trial_name": null, "trial_params": null}